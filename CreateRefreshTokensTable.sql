-- Create RefreshTokens table manually
-- Run this script in your SQL Server database to create the RefreshTokens table

CREATE TABLE [dbo].[RefreshTokens] (
    [Id] int IDENTITY(1,1) NOT NULL,
    [Token] nvarchar(500) NOT NULL,
    [ExpiresAt] datetime2 NOT NULL,
    [IsRevoked] bit NOT NULL DEFAULT 0,
    [RevokedAt] datetime2 NULL,
    [RevokedByIp] nvarchar(45) NULL,
    [ReplacedByToken] nvarchar(500) NULL,
    [ReasonRevoked] nvarchar(200) NULL,
    [UserId] int NOT NULL,
    [CreatedAt] datetime2 NOT NULL DEFAULT GETUTCDATE(),
    [UpdatedAt] datetime2 NULL,
    [CreatedBy] nvarchar(max) NOT NULL DEFAULT '',
    [UpdatedBy] nvarchar(max) NULL,
    [IsDeleted] bit NOT NULL DEFAULT 0,
    [DeletedAt] datetime2 NULL,
    
    CONSTRAINT [PK_RefreshTokens] PRIMARY KEY ([Id]),
    CONSTRAINT [FK_RefreshTokens_Users_UserId] FOREIGN KEY ([UserId]) REFERENCES [Users] ([Id]) ON DELETE CASCADE
);

-- Create indexes for performance
CREATE UNIQUE NONCLUSTERED INDEX [IX_RefreshTokens_Token] ON [dbo].[RefreshTokens] ([Token]);
CREATE NONCLUSTERED INDEX [IX_RefreshTokens_UserId] ON [dbo].[RefreshTokens] ([UserId]);
CREATE NONCLUSTERED INDEX [IX_RefreshTokens_ExpiresAt] ON [dbo].[RefreshTokens] ([ExpiresAt]);

PRINT 'RefreshTokens table created successfully!';
