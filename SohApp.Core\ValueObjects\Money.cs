namespace SohApp.Core.ValueObjects;

public class Money : IEquatable<Money>, IComparable<Money>
{
    public decimal Amount { get; }
    public string Currency { get; }

    public bool IsZero => Amount == 0;
    public bool IsPositive => Amount > 0;
    public bool IsNegative => Amount < 0;

    private Money(decimal amount, string currency, bool allowNegative)
    {
        if (string.IsNullOrWhiteSpace(currency))
            throw new ArgumentException("Currency cannot be empty", nameof(currency));

        if (currency.Length != 3)
            throw new ArgumentException("Currency must be a 3-letter ISO code", nameof(currency));

        if (!allowNegative && amount < 0)
            throw new ArgumentException("Amount cannot be negative", nameof(amount));

        Amount = Math.Round(amount, 2);
        Currency = currency.ToUpperInvariant();
    }

    public Money(decimal amount, string currency = "USD") : this(amount, currency, false)
    {
    }

    /// <summary>
    /// Creates a Money instance allowing negative amounts (for calculations)
    /// </summary>
    public static Money CreateAllowingNegative(decimal amount, string currency = "USD")
    {
        return new Money(amount, currency, true);
    }

    public static Money Zero(string currency = "USD") => new(0, currency);

    public Money Add(Money other)
    {
        ValidateSameCurrency(other);
        return CreateAllowingNegative(Amount + other.Amount, Currency);
    }

    public Money Subtract(Money other)
    {
        ValidateSameCurrency(other);
        var result = CreateAllowingNegative(Amount - other.Amount, Currency);

        // Business rule: Most operations shouldn't result in negative money
        if (result.IsNegative)
            throw new InvalidOperationException("Operation would result in negative amount");

        return result;
    }

    public Money SubtractAllowingNegative(Money other)
    {
        ValidateSameCurrency(other);
        return CreateAllowingNegative(Amount - other.Amount, Currency);
    }

    public Money Multiply(decimal factor)
    {
        if (factor < 0)
            throw new ArgumentException("Factor cannot be negative", nameof(factor));

        return CreateAllowingNegative(Amount * factor, Currency);
    }

    public Money MultiplyAllowingNegative(decimal factor)
    {
        return CreateAllowingNegative(Amount * factor, Currency);
    }

    public Money Divide(decimal divisor)
    {
        if (divisor == 0)
            throw new DivideByZeroException("Cannot divide by zero");

        if (divisor < 0)
            throw new ArgumentException("Divisor cannot be negative", nameof(divisor));

        return CreateAllowingNegative(Amount / divisor, Currency);
    }

    public Money Abs() => CreateAllowingNegative(Math.Abs(Amount), Currency);

    public Money Negate() => CreateAllowingNegative(-Amount, Currency);

    private void ValidateSameCurrency(Money other)
    {
        ArgumentNullException.ThrowIfNull(other);

        if (Currency != other.Currency)
            throw new InvalidOperationException($"Cannot perform operation with different currencies: {Currency} and {other.Currency}");
    }

    public bool Equals(Money? other) =>
        other is not null && Amount == other.Amount && Currency == other.Currency;

    public override bool Equals(object? obj) => Equals(obj as Money);

    public override int GetHashCode() => HashCode.Combine(Amount, Currency);

    public override string ToString() => $"{Amount:F2} {Currency}";

    public string ToString(string format) => $"{Amount.ToString(format)} {Currency}";

    public int CompareTo(Money? other)
    {
        if (other is null) return 1;
        ValidateSameCurrency(other);
        return Amount.CompareTo(other.Amount);
    }

    // Operators
    public static Money operator +(Money left, Money right) => left.Add(right);
    public static Money operator -(Money left, Money right) => left.Subtract(right);
    public static Money operator *(Money left, decimal right) => left.Multiply(right);
    public static Money operator /(Money left, decimal right) => left.Divide(right);
    public static Money operator -(Money money) => money.Negate();

    public static bool operator ==(Money left, Money right) =>
        left?.Equals(right) ?? right is null;
    public static bool operator !=(Money left, Money right) => !(left == right);

    public static bool operator <(Money left, Money right) =>
        left?.CompareTo(right) < 0;
    public static bool operator >(Money left, Money right) =>
        left?.CompareTo(right) > 0;
    public static bool operator <=(Money left, Money right) =>
        left?.CompareTo(right) <= 0;
    public static bool operator >=(Money left, Money right) =>
        left?.CompareTo(right) >= 0;
}
