using MediatR;
using SohApp.Application.Common;
using SohApp.Core.Enums;

namespace SohApp.Application.Features.Users.Commands.CreateUser;

public class CreateUserCommand : IRequest<Result<int>>
{
    public string FirstName { get; set; } = string.Empty;
    public string LastName { get; set; } = string.Empty;
    public string Email { get; set; } = string.Empty;
    public string Password { get; set; } = string.Empty;
    public UserRole Role { get; set; }
}
