using System.Net;
using System.Text.Json;
using SohApp.API.Models;
using SohApp.Core.Exceptions;
using System.Diagnostics;

namespace SohApp.API.Middleware;

public class ExceptionHandlingMiddleware
{
    private readonly RequestDelegate _next;
    private readonly ILogger<ExceptionHandlingMiddleware> _logger;
    private readonly IWebHostEnvironment _environment;

    public ExceptionHandlingMiddleware(
        RequestDelegate next,
        ILogger<ExceptionHandlingMiddleware> logger,
        IWebHostEnvironment environment)
    {
        _next = next;
        _logger = logger;
        _environment = environment;
    }

    public async Task InvokeAsync(HttpContext context)
    {
        var stopwatch = Stopwatch.StartNew();

        try
        {
            await _next(context);
        }
        catch (Exception ex)
        {
            stopwatch.Stop();

            // Log with structured data but avoid sensitive information
            using var scope = _logger.BeginScope(new Dictionary<string, object>
            {
                ["RequestPath"] = context.Request.Path,
                ["RequestMethod"] = context.Request.Method,
                ["TraceId"] = context.TraceIdentifier,
                ["UserId"] = context.User?.Identity?.Name ?? "Anonymous",
                ["ElapsedMs"] = stopwatch.ElapsedMilliseconds
            });

            LogException(ex);
            await HandleExceptionAsync(context, ex);
        }
    }

    private void LogException(Exception exception)
    {
        switch (exception)
        {
            case ValidationException validationEx:
                _logger.LogWarning("Validation failed: {Errors}", string.Join(", ", validationEx.Errors));
                break;

            case NotFoundException notFoundEx:
                _logger.LogWarning("Resource not found: {Message}", notFoundEx.Message);
                break;

            case BusinessRuleException businessEx:
                _logger.LogWarning("Business rule violation: {Message}", businessEx.Message);
                break;

            case DomainException domainEx:
                _logger.LogWarning("Domain exception: {Message}", domainEx.Message);
                break;

            case UnauthorizedAccessException:
                _logger.LogWarning("Unauthorized access attempt");
                break;

            default:
                // Only log full exception details for unexpected errors
                _logger.LogError(exception, "Unhandled exception occurred: {ExceptionType}", exception.GetType().Name);
                break;
        }
    }

    private async Task HandleExceptionAsync(HttpContext context, Exception exception)
    {
        // Prevent response modification if already started
        if (context.Response.HasStarted)
        {
            _logger.LogWarning("Cannot modify response, it has already started");
            return;
        }

        context.Response.ContentType = "application/json";

        // Add security headers
        context.Response.Headers["X-Content-Type-Options"] = "nosniff";
        context.Response.Headers["X-Frame-Options"] = "DENY";
        context.Response.Headers["X-XSS-Protection"] = "1; mode=block";

        var response = new ErrorResponse
        {
            TraceId = context.TraceIdentifier,
            Timestamp = DateTime.UtcNow
        };

        switch (exception)
        {
            case ValidationException validationEx:
                response.Message = "The request contains validation errors";
                response.Errors = SanitizeErrors(validationEx.Errors);
                context.Response.StatusCode = (int)HttpStatusCode.BadRequest;
                break;

            case NotFoundException notFoundEx:
                response.Message = "The requested resource was not found";
                response.Errors = new[] { SanitizeErrorMessage(notFoundEx.Message) };
                context.Response.StatusCode = (int)HttpStatusCode.NotFound;
                break;

            case BusinessRuleException businessEx:
                response.Message = "The request violates business rules";
                response.Errors = new[] { SanitizeErrorMessage(businessEx.Message) };
                context.Response.StatusCode = (int)HttpStatusCode.BadRequest;
                break;

            case DomainException domainEx:
                response.Message = "A domain error occurred";
                response.Errors = new[] { SanitizeErrorMessage(domainEx.Message) };
                context.Response.StatusCode = (int)HttpStatusCode.BadRequest;
                break;

            case UnauthorizedAccessException:
                response.Message = "Access denied";
                response.Errors = new[] { "You do not have permission to access this resource" };
                context.Response.StatusCode = (int)HttpStatusCode.Unauthorized;
                break;

            case TimeoutException:
                response.Message = "Request timeout";
                response.Errors = new[] { "The request took too long to process" };
                context.Response.StatusCode = (int)HttpStatusCode.RequestTimeout;
                break;

            default:
                response.Message = "An internal server error occurred";
                response.Errors = _environment.IsDevelopment()
                    ? new[] { exception.Message }
                    : new[] { "Please try again later or contact support if the problem persists" };
                context.Response.StatusCode = (int)HttpStatusCode.InternalServerError;
                break;
        }

        var jsonOptions = new JsonSerializerOptions
        {
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
            WriteIndented = _environment.IsDevelopment()
        };

        var jsonResponse = JsonSerializer.Serialize(response, jsonOptions);
        await context.Response.WriteAsync(jsonResponse);
    }

    private static IEnumerable<string> SanitizeErrors(IEnumerable<string> errors)
    {
        return errors.Select(SanitizeErrorMessage);
    }

    private static string SanitizeErrorMessage(string message)
    {
        // Remove potentially sensitive information
        if (message.Contains("ConnectionString", StringComparison.OrdinalIgnoreCase) ||
            message.Contains("Password", StringComparison.OrdinalIgnoreCase) ||
            message.Contains("Token", StringComparison.OrdinalIgnoreCase) ||
            message.Contains("Key", StringComparison.OrdinalIgnoreCase))
        {
            return "A system error occurred";
        }

        return message;
    }
}
