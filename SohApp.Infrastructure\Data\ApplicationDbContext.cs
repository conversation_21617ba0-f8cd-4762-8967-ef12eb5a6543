using System.Reflection;
using Microsoft.EntityFrameworkCore;
using SohApp.Core.Entities;
using SohApp.Core.Entities.Base;
using SohApp.Core.Events.Base;
using SohApp.Core.Interfaces;
using SohApp.Core.Interfaces.Services;

namespace SohApp.Infrastructure.Data;

public class ApplicationDbContext : DbContext
{
    private readonly ICurrentUserService _currentUserService;
    private readonly IDomainEventDispatcher _domainEventDispatcher;

    public ApplicationDbContext(
        DbContextOptions<ApplicationDbContext> options,
        ICurrentUserService currentUserService,
        IDomainEventDispatcher domainEventDispatcher) : base(options)
    {
        _currentUserService = currentUserService;
        _domainEventDispatcher = domainEventDispatcher;
    }

    public DbSet<User> Users => Set<User>();
    public DbSet<Product> Products => Set<Product>();
    public DbSet<Order> Orders => Set<Order>();
    public DbSet<OrderItem> OrderItems => Set<OrderItem>();
    public DbSet<Category> Categories => Set<Category>();
    public DbSet<RefreshToken> RefreshTokens => Set<RefreshToken>();

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        base.OnModelCreating(modelBuilder);

        // Apply all configurations from assembly
        modelBuilder.ApplyConfigurationsFromAssembly(typeof(ApplicationDbContext).Assembly);

        // Apply global query filters
        foreach (var entityType in modelBuilder.Model.GetEntityTypes())
        {
            if (typeof(ISoftDeletable).IsAssignableFrom(entityType.ClrType))
            {
                var method = typeof(ApplicationDbContext)
                    .GetMethod(nameof(SetSoftDeleteFilter), BindingFlags.NonPublic | BindingFlags.Static)!
                    .MakeGenericMethod(entityType.ClrType);
                method.Invoke(null, new object[] { modelBuilder });
            }
        }
    }

    private static void SetSoftDeleteFilter<T>(ModelBuilder modelBuilder) where T : class, ISoftDeletable
    {
        modelBuilder.Entity<T>().HasQueryFilter(e => !e.IsDeleted);
    }

    public override async Task<int> SaveChangesAsync(CancellationToken cancellationToken = default)
    {
        return await SaveChangesAsync(true, cancellationToken);
    }

    /// <summary>
    /// SaveChanges with option to dispatch domain events
    /// </summary>
    public new async Task<int> SaveChangesAsync(bool dispatchDomainEvents, CancellationToken cancellationToken = default)
    {
        // Update audit fields
        UpdateAuditFields();

        // Save changes to database
        var result = await base.SaveChangesAsync(cancellationToken);

        // Dispatch domain events if requested and not within a retry strategy context
        if (dispatchDomainEvents)
        {
            try
            {
                await DispatchDomainEventsAsync(cancellationToken);
            }
            catch (Exception ex)
            {
                // Log domain event dispatch errors but don't fail the main operation
                // Domain events are eventually consistent and can be retried
                System.Diagnostics.Debug.WriteLine($"Domain event dispatch failed: {ex.Message}");
                // TODO: Add proper logging here
            }
        }

        return result;
    }

    private void UpdateAuditFields()
    {
        var currentUser = GetCurrentUser();
        var now = DateTime.UtcNow;

        var auditableEntries = ChangeTracker.Entries<IAuditableEntity>().ToList();

        foreach (var entry in auditableEntries)
        {
            switch (entry.State)
            {
                case EntityState.Added:
                    entry.Entity.CreatedBy = currentUser;
                    entry.Entity.CreatedAt = now;
                    break;

                case EntityState.Modified:
                    entry.Entity.UpdatedBy = currentUser;
                    entry.Entity.UpdatedAt = now;
                    break;

                case EntityState.Deleted:
                    if (entry.Entity is ISoftDeletable softDeletable)
                    {
                        // Convert hard delete to soft delete
                        entry.State = EntityState.Modified;
                        softDeletable.IsDeleted = true;
                        softDeletable.DeletedAt = now;
                        entry.Entity.UpdatedBy = currentUser;
                        entry.Entity.UpdatedAt = now;
                    }
                    break;
            }
        }
    }

    private string GetCurrentUser()
    {
        try
        {
            return _currentUserService?.UserId ?? "System";
        }
        catch (Exception)
        {
            // Fallback if current user service fails
            return "System";
        }
    }

    private async Task DispatchDomainEventsAsync(CancellationToken cancellationToken)
    {
        // Optimize: Get entities with domain events in a single pass
        var entitiesWithEvents = new List<(BaseEntity Entity, IReadOnlyCollection<IDomainEvent> Events)>();

        foreach (var entry in ChangeTracker.Entries<BaseEntity>())
        {
            if (entry.Entity.HasDomainEvents)
            {
                entitiesWithEvents.Add((entry.Entity, entry.Entity.DomainEvents));
            }
        }

        if (!entitiesWithEvents.Any())
        {
            return; // No events to dispatch
        }

        // Clear domain events from entities first
        foreach (var (entity, _) in entitiesWithEvents)
        {
            entity.ClearDomainEvents();
        }

        // Dispatch all events
        var dispatchTasks = new List<Task>();

        foreach (var (_, events) in entitiesWithEvents)
        {
            foreach (var domainEvent in events)
            {
                try
                {
                    dispatchTasks.Add(_domainEventDispatcher.DispatchAsync(domainEvent, cancellationToken));
                }
                catch (Exception ex)
                {
                    // Log error but don't fail the entire operation
                    // TODO: Add proper logging
                    System.Diagnostics.Debug.WriteLine($"Error dispatching domain event {domainEvent.GetType().Name}: {ex.Message}");
                }
            }
        }

        // Wait for all events to be dispatched
        if (dispatchTasks.Any())
        {
            await Task.WhenAll(dispatchTasks);
        }
    }
}
