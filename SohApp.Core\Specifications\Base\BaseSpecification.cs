using System.Linq.Expressions;
using SohApp.Core.Interfaces;

namespace SohApp.Core.Specifications.Base;

public abstract class BaseSpecification<T> : ISpecification<T>
{
    public Expression<Func<T, bool>>? Criteria { get; private set; }
    public List<Expression<Func<T, object>>> Includes { get; } = new();
    public List<string> IncludeStrings { get; } = new();
    public Expression<Func<T, object>>? OrderBy { get; private set; }
    public Expression<Func<T, object>>? OrderByDescending { get; private set; }
    public List<(Expression<Func<T, object>> KeySelector, bool Descending)> ThenByExpressions { get; } = new();
    public int Take { get; private set; }
    public int Skip { get; private set; }
    public bool IsPagingEnabled { get; private set; }
    public bool IsSplitQuery { get; private set; }
    public bool AsNoTracking { get; private set; }

    protected BaseSpecification() { }

    protected BaseSpecification(Expression<Func<T, bool>> criteria)
    {
        Criteria = criteria;
    }

    protected virtual void AddInclude(Expression<Func<T, object>> includeExpression)
    {
        Includes.Add(includeExpression);
    }

    protected virtual void AddInclude(string includeString)
    {
        IncludeStrings.Add(includeString);
    }

    public virtual void ApplyPaging(int skip, int take)
    {
        Skip = skip;
        Take = take;
        IsPagingEnabled = true;
    }

    protected virtual void ApplyOrderBy(Expression<Func<T, object>> orderByExpression)
    {
        OrderBy = orderByExpression;
    }

    protected virtual void ApplyOrderByDescending(Expression<Func<T, object>> orderByDescExpression)
    {
        OrderByDescending = orderByDescExpression;
    }

    protected virtual void ApplyThenBy(Expression<Func<T, object>> thenByExpression)
    {
        ThenByExpressions.Add((thenByExpression, false));
    }

    protected virtual void ApplyThenByDescending(Expression<Func<T, object>> thenByDescExpression)
    {
        ThenByExpressions.Add((thenByDescExpression, true));
    }

    protected virtual void EnableSplitQuery()
    {
        IsSplitQuery = true;
    }

    protected virtual void EnableNoTracking()
    {
        AsNoTracking = true;
    }

    protected virtual void SetCriteria(Expression<Func<T, bool>> criteria)
    {
        Criteria = criteria;
    }
}
