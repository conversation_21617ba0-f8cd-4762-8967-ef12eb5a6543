using SohApp.Core.Constants;
using SohApp.Core.Entities.Base;
using SohApp.Core.Enums;
using SohApp.Core.Events.UserEvents;
using SohApp.Core.Exceptions;
using SohApp.Core.ValueObjects;

namespace SohApp.Core.Entities;

public class User : BaseEntity
{
    private string _firstName = string.Empty;
    private string _lastName = string.Empty;

    public string FirstName
    {
        get => _firstName;
        set => _firstName = ValidateName(value, nameof(FirstName));
    }

    public string LastName
    {
        get => _lastName;
        set => _lastName = ValidateName(value, nameof(LastName));
    }

    public Email Email { get; set; } = null!;
    public string PasswordHash { get; set; } = string.Empty;
    public UserRole Role { get; set; }
    public bool IsActive { get; set; } = true;
    public DateTime? LastLoginAt { get; private set; }

    // Navigation properties - removed virtual to prevent lazy loading issues
    public ICollection<Order> Orders { get; set; } = new List<Order>();
    public ICollection<RefreshToken> RefreshTokens { get; set; } = new List<RefreshToken>();

    public string FullName => $"{FirstName} {LastName}".Trim();

    /// <summary>
    /// Activates the user account
    /// </summary>
    /// <exception cref="BusinessRuleException">Thrown when user is already active</exception>
    public void Activate()
    {
        if (IsActive)
            throw new BusinessRuleException("User is already active");

        IsActive = true;
        AddDomainEvent(new UserActivatedEvent(Id, Email.Value));
    }

    /// <summary>
    /// Deactivates the user account
    /// </summary>
    /// <exception cref="BusinessRuleException">Thrown when user is already inactive</exception>
    public void Deactivate()
    {
        if (!IsActive)
            throw new BusinessRuleException("User is already inactive");

        IsActive = false;
        AddDomainEvent(new UserDeactivatedEvent(Id, Email.Value));
    }

    /// <summary>
    /// Updates the last login timestamp
    /// </summary>
    /// <param name="loginTime">The login time (defaults to current UTC time if not provided)</param>
    public void UpdateLastLogin(DateTime? loginTime = null)
    {
        LastLoginAt = loginTime ?? DateTime.UtcNow;
    }

    /// <summary>
    /// Updates the user's profile information
    /// </summary>
    /// <param name="firstName">The new first name</param>
    /// <param name="lastName">The new last name</param>
    /// <exception cref="ArgumentException">Thrown when names are invalid</exception>
    public void UpdateProfile(string firstName, string lastName)
    {
        var oldFullName = FullName;

        FirstName = firstName; // Will validate through property setter
        LastName = lastName;   // Will validate through property setter

        // Only raise event if something actually changed
        if (FullName != oldFullName)
        {
            AddDomainEvent(new UserUpdatedEvent(Id, Email.Value, FullName));
        }
    }

    /// <summary>
    /// Changes the user's role
    /// </summary>
    /// <param name="newRole">The new role to assign</param>
    /// <exception cref="BusinessRuleException">Thrown when role change is not allowed</exception>
    public void ChangeRole(UserRole newRole)
    {
        if (Role == newRole)
            return; // No change needed

        // Business rule: Cannot change role of inactive user
        if (!IsActive)
            throw new BusinessRuleException("Cannot change role of inactive user");

        var oldRole = Role;
        Role = newRole;

        AddDomainEvent(new UserRoleChangedEvent(Id, Email.Value, oldRole, newRole));
    }

    /// <summary>
    /// Validates a name field
    /// </summary>
    /// <param name="name">The name to validate</param>
    /// <param name="fieldName">The field name for error messages</param>
    /// <returns>The validated name</returns>
    /// <exception cref="ArgumentException">Thrown when name is invalid</exception>
    private static string ValidateName(string name, string fieldName)
    {
        if (string.IsNullOrWhiteSpace(name))
            throw new ArgumentException($"{fieldName} cannot be empty", fieldName);

        if (name.Length > DomainConstants.User.NameMaxLength)
            throw new ArgumentException($"{fieldName} cannot exceed {DomainConstants.User.NameMaxLength} characters", fieldName);

        // Remove extra whitespace
        return name.Trim();
    }
}
