using Microsoft.Extensions.Logging;
using SohApp.Core.Events.UserEvents;
using SohApp.Core.Interfaces;
using SohApp.Core.Interfaces.Services;

namespace SohApp.Application.EventHandlers.UserEventHandlers;

public class UserCreatedEventHandler : IDomainEventHandler<UserCreatedEvent>
{
    private readonly IEmailService _emailService;
    private readonly ILogger<UserCreatedEventHandler> _logger;

    public UserCreatedEventHandler(
        IEmailService emailService,
        ILogger<UserCreatedEventHandler> logger)
    {
        _emailService = emailService;
        _logger = logger;
    }

    public async Task Handle(UserCreatedEvent domainEvent, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Handling UserCreatedEvent for user {UserId}", domainEvent.UserId);

            // Send welcome email
            await _emailService.SendWelcomeEmailAsync(
                domainEvent.Email,
                domainEvent.FullName,
                cancellationToken);

            _logger.LogInformation("Welcome email sent successfully for user {UserId}", domainEvent.UserId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error handling UserCreatedEvent for user {UserId}", domainEvent.UserId);
            // Don't rethrow - we don't want email failures to break user creation
        }
    }
}
