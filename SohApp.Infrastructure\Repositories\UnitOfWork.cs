using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Storage;
using SohApp.Core.Entities.Base;
using SohApp.Core.Interfaces;
using SohApp.Core.Interfaces.Repositories;
using SohApp.Infrastructure.Data;

namespace SohApp.Infrastructure.Repositories;

public class UnitOfWork : IUnitOfWork
{
    private readonly ApplicationDbContext _context;
    private readonly Dictionary<Type, object> _repositories = new();
    private IDbContextTransaction? _transaction;

    // Cached repository instances
    private IUserRepository? _userRepository;
    private IProductRepository? _productRepository;
    private IOrderRepository? _orderRepository;
    private ICategoryRepository? _categoryRepository;

    public UnitOfWork(ApplicationDbContext context)
    {
        _context = context;
    }

    public IUserRepository Users => _userRepository ??= new UserRepository(_context);
    public IProductRepository Products => _productRepository ??= new ProductRepository(_context);
    public IOrderRepository Orders => _orderRepository ??= new OrderRepository(_context);
    public ICategoryRepository Categories => _categoryRepository ??= new CategoryRepository(_context);

    public async Task<int> SaveChangesAsync(CancellationToken cancellationToken = default)
    {
        return await _context.SaveChangesAsync(cancellationToken);
    }

    public async Task<int> SaveChangesAsync(string userId, CancellationToken cancellationToken = default)
    {
        // The userId is handled by the CurrentUserService in the DbContext
        return await _context.SaveChangesAsync(cancellationToken);
    }

    /// <summary>
    /// SaveChanges without dispatching domain events (for use within execution strategies)
    /// </summary>
    public async Task<int> SaveChangesWithoutEventsAsync(CancellationToken cancellationToken = default)
    {
        return await _context.SaveChangesAsync(false, cancellationToken);
    }

    public async Task BeginTransactionAsync(CancellationToken cancellationToken = default)
    {
        // Only create transaction if one doesn't already exist
        if (_transaction == null)
        {
            _transaction = await _context.Database.BeginTransactionAsync(cancellationToken);
        }
    }

    public async Task CommitTransactionAsync(CancellationToken cancellationToken = default)
    {
        if (_transaction != null)
        {
            await _transaction.CommitAsync(cancellationToken);
            await _transaction.DisposeAsync();
            _transaction = null;
        }
    }

    public async Task RollbackTransactionAsync(CancellationToken cancellationToken = default)
    {
        if (_transaction != null)
        {
            await _transaction.RollbackAsync(cancellationToken);
            await _transaction.DisposeAsync();
            _transaction = null;
        }
    }

    /// <summary>
    /// Executes a function within a transaction, handling execution strategies properly
    /// Uses the DbContext's execution strategy to handle retries correctly
    /// </summary>
    public async Task<T> ExecuteInTransactionAsync<T>(Func<Task<T>> operation, CancellationToken cancellationToken = default)
    {
        // Use the DbContext's execution strategy to handle the entire operation
        // This is the correct way to handle transactions with retry strategies
        var strategy = _context.Database.CreateExecutionStrategy();

        return await strategy.ExecuteAsync(async () =>
        {
            // The execution strategy will handle retries for the entire operation
            using var transaction = await _context.Database.BeginTransactionAsync(cancellationToken);
            try
            {
                var result = await operation();
                await transaction.CommitAsync(cancellationToken);
                return result;
            }
            catch
            {
                await transaction.RollbackAsync(cancellationToken);
                throw;
            }
        });
    }

    /// <summary>
    /// Executes an action within a transaction, handling execution strategies properly
    /// </summary>
    public async Task ExecuteInTransactionAsync(Func<Task> operation, CancellationToken cancellationToken = default)
    {
        await ExecuteInTransactionAsync(async () =>
        {
            await operation();
            return 0; // Dummy return value
        }, cancellationToken);
    }

    /// <summary>
    /// Determines if an exception is transient and should be retried
    /// </summary>
    private static bool IsTransientException(Exception exception)
    {
        // Add logic to determine if the exception is transient
        // For now, we'll be conservative and not retry most exceptions
        return exception is TimeoutException ||
               (exception.Message?.Contains("timeout", StringComparison.OrdinalIgnoreCase) == true) ||
               (exception.Message?.Contains("deadlock", StringComparison.OrdinalIgnoreCase) == true);
    }

    public IGenericRepository<T> Repository<T>() where T : BaseEntity
    {
        return GetRepository<IGenericRepository<T>>(() => new GenericRepository<T>(_context));
    }

    public async Task PublishDomainEventsAsync(CancellationToken cancellationToken = default)
    {
        // Domain events are handled automatically in the DbContext SaveChangesAsync method
        await Task.CompletedTask;
    }

    private TRepository GetRepository<TRepository>(Func<TRepository> factory)
    {
        var type = typeof(TRepository);
        if (!_repositories.ContainsKey(type))
        {
            _repositories[type] = factory();
        }
        return (TRepository)_repositories[type];
    }

    public void Dispose()
    {
        _transaction?.Dispose();
        _context.Dispose();
    }
}
