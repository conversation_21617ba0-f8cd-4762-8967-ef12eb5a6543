using MediatR;
using SohApp.Application.Common;
using SohApp.Application.DTOs.Product;

namespace SohApp.Application.Features.Products.Queries.GetProducts;

public class GetProductsQuery : IRequest<Result<PagedResult<ProductDto>>>
{
    public int Page { get; set; } = 1;
    public int PageSize { get; set; } = 10;
    public int? CategoryId { get; set; }
    public string? SearchTerm { get; set; }
    public decimal? MinPrice { get; set; }
    public decimal? MaxPrice { get; set; }
    public bool? InStock { get; set; }
    public string? SortBy { get; set; }
    public bool SortDescending { get; set; }
}
