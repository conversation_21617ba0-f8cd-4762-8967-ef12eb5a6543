using AutoMapper;
using MediatR;
using Microsoft.Extensions.Logging;
using SohApp.Application.Common;
using SohApp.Application.DTOs.User;
using SohApp.Application.Features.Users.Queries.GetUsers.Specifications;
using SohApp.Core.Entities;
using SohApp.Core.Exceptions;
using SohApp.Core.Interfaces;

namespace SohApp.Application.Features.Users.Queries.GetUsers;

public class GetUsersQueryHandler : IRequestHandler<GetUsersQuery, Result<PagedResult<UserDto>>>
{
    private readonly IUnitOfWork _unitOfWork;
    private readonly IMapper _mapper;
    private readonly ILogger<GetUsersQueryHandler> _logger;

    public GetUsersQueryHandler(
        IUnitOfWork unitOfWork,
        IMapper mapper,
        ILogger<GetUsersQueryHandler> logger)
    {
        _unitOfWork = unitOfWork;
        _mapper = mapper;
        _logger = logger;
    }

    public async Task<Result<PagedResult<UserDto>>> Handle(GetUsersQuery request, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogDebug("Retrieving users with filters: Role={Role}, IsActive={IsActive}, SearchTerm={SearchTerm}, Page={Page}, PageSize={PageSize}",
                request.Role, request.IsActive, request.SearchTerm, request.Page, request.PageSize);

            // Validate pagination parameters
            if (request.Page < 1)
                return Result<PagedResult<UserDto>>.Failure("Page number must be greater than 0");

            if (request.PageSize < 1 || request.PageSize > 100)
                return Result<PagedResult<UserDto>>.Failure("Page size must be between 1 and 100");

            var spec = new GetUsersSpecification(
                request.Role,
                request.IsActive,
                request.SearchTerm,
                request.SortBy,
                request.SortDescending);

            // Get total count first (without pagination)
            var totalCount = await _unitOfWork.Users.CountAsync(spec, cancellationToken);

            // Apply pagination to specification
            spec.ApplyPaging((request.Page - 1) * request.PageSize, request.PageSize);

            // Get paginated data
            var users = await _unitOfWork.Users.GetAsync(spec, cancellationToken);
            var userDtos = _mapper.Map<IReadOnlyList<UserDto>>(users);

            var result = new PagedResult<UserDto>
            {
                Data = userDtos,
                TotalCount = totalCount,
                Page = request.Page,
                PageSize = request.PageSize,
                TotalPages = (int)Math.Ceiling(totalCount / (double)request.PageSize),
                HasNextPage = request.Page < (int)Math.Ceiling(totalCount / (double)request.PageSize),
                HasPreviousPage = request.Page > 1
            };

            _logger.LogDebug("Successfully retrieved {Count} users out of {TotalCount} total",
                userDtos.Count, totalCount);

            return Result<PagedResult<UserDto>>.Success(result);
        }
        catch (ValidationException ex)
        {
            _logger.LogWarning(ex, "Validation error while retrieving users: {Errors}", string.Join(", ", ex.Errors));
            return Result<PagedResult<UserDto>>.Failure(ex.Errors.First());
        }
        catch (BusinessRuleException ex)
        {
            _logger.LogWarning(ex, "Business rule violation while retrieving users: {Message}", ex.Message);
            return Result<PagedResult<UserDto>>.Failure(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Unexpected error retrieving users with filters: Role={Role}, IsActive={IsActive}, SearchTerm={SearchTerm}",
                request.Role, request.IsActive, request.SearchTerm);
            return Result<PagedResult<UserDto>>.Failure("An unexpected error occurred while retrieving users. Please try again.");
        }
    }
}
