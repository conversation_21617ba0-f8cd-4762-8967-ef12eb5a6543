using System.Linq.Expressions;
using Microsoft.EntityFrameworkCore;
using SohApp.Core.Entities.Base;
using SohApp.Core.Interfaces;
using SohApp.Core.Interfaces.Repositories;
using SohApp.Infrastructure.Data;
using SohApp.Infrastructure.Specifications;

namespace SohApp.Infrastructure.Repositories;

public class GenericRepository<T> : IGenericRepository<T> where T : BaseEntity
{
    protected readonly ApplicationDbContext _context;
    protected readonly DbSet<T> _dbSet;

    public GenericRepository(ApplicationDbContext context)
    {
        _context = context;
        _dbSet = context.Set<T>();
    }

    public virtual async Task<T?> GetByIdAsync(int id, CancellationToken cancellationToken = default)
    {
        return await _dbSet.FindAsync(new object[] { id }, cancellationToken);
    }

    public virtual async Task<T?> GetByIdAsync(int id, params Expression<Func<T, object>>[] includes)
    {
        IQueryable<T> query = _dbSet;

        foreach (var include in includes)
        {
            query = query.Include(include);
        }

        return await query.FirstOrDefaultAsync(x => x.Id == id);
    }

    /// <summary>
    /// Gets all entities - USE WITH CAUTION! Consider using GetAsync with specification for better performance
    /// </summary>
    [Obsolete("Use GetAsync with specification for better performance and filtering")]
    public virtual async Task<IReadOnlyList<T>> GetAllAsync(CancellationToken cancellationToken = default)
    {
        // Add warning log for performance monitoring
        System.Diagnostics.Debug.WriteLine($"WARNING: GetAllAsync called for {typeof(T).Name} - consider using specification pattern");

        return await _dbSet
            .AsNoTracking() // Add no tracking for read-only operations
            .ToListAsync(cancellationToken);
    }

    /// <summary>
    /// Gets entities with read-only tracking for better performance
    /// </summary>
    public virtual async Task<IReadOnlyList<T>> GetAllReadOnlyAsync(CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .AsNoTracking()
            .Where(x => !x.IsDeleted) // Filter soft-deleted entities
            .ToListAsync(cancellationToken);
    }

    public virtual async Task<IReadOnlyList<T>> GetAsync(ISpecification<T> spec, CancellationToken cancellationToken = default)
    {
        return await ApplySpecification(spec).ToListAsync(cancellationToken);
    }

    public virtual async Task<T?> GetEntityWithSpec(ISpecification<T> spec, CancellationToken cancellationToken = default)
    {
        return await ApplySpecification(spec).FirstOrDefaultAsync(cancellationToken);
    }

    public virtual async Task<int> CountAsync(ISpecification<T> spec, CancellationToken cancellationToken = default)
    {
        // Optimize count queries by only applying criteria and ignoring includes/ordering
        var query = _dbSet.AsQueryable();

        if (spec.Criteria != null)
        {
            query = query.Where(spec.Criteria);
        }

        return await query.AsNoTracking().CountAsync(cancellationToken);
    }

    public virtual async Task<bool> ExistsAsync(int id, CancellationToken cancellationToken = default)
    {
        return await _dbSet.AnyAsync(x => x.Id == id, cancellationToken);
    }

    public virtual async Task<bool> ExistsAsync(ISpecification<T> spec, CancellationToken cancellationToken = default)
    {
        return await ApplySpecification(spec).AnyAsync(cancellationToken);
    }

    public virtual async Task<T> AddAsync(T entity, CancellationToken cancellationToken = default)
    {
        await _dbSet.AddAsync(entity, cancellationToken);
        return entity;
    }

    public virtual async Task<IEnumerable<T>> AddRangeAsync(IEnumerable<T> entities, CancellationToken cancellationToken = default)
    {
        await _dbSet.AddRangeAsync(entities, cancellationToken);
        return entities;
    }

    public virtual void Update(T entity)
    {
        // More efficient update pattern
        if (_context.Entry(entity).State == EntityState.Detached)
        {
            _dbSet.Attach(entity);
        }

        _context.Entry(entity).State = EntityState.Modified;

        // Set audit fields
        entity.UpdatedAt = DateTime.UtcNow;
        // TODO: Set UpdatedBy from current user context
    }

    public virtual void UpdateRange(IEnumerable<T> entities)
    {
        var entityList = entities.ToList();
        var now = DateTime.UtcNow;

        // Set audit fields for all entities
        foreach (var entity in entityList)
        {
            entity.UpdatedAt = now;
            // TODO: Set UpdatedBy from current user context
        }

        _dbSet.UpdateRange(entityList);
    }

    /// <summary>
    /// Efficiently updates specific properties without loading the entity
    /// </summary>
    public virtual async Task<bool> UpdatePropertiesAsync(int id, Expression<Func<T, object>> propertyExpression, object value, CancellationToken cancellationToken = default)
    {
        var entity = await _dbSet.FindAsync(new object[] { id }, cancellationToken);
        if (entity == null) return false;

        var propertyInfo = ((MemberExpression)propertyExpression.Body).Member as System.Reflection.PropertyInfo;
        if (propertyInfo != null)
        {
            propertyInfo.SetValue(entity, value);
            entity.UpdatedAt = DateTime.UtcNow;
            return true;
        }

        return false;
    }

    public virtual void Delete(T entity)
    {
        if (_context.Entry(entity).State == EntityState.Detached)
        {
            _dbSet.Attach(entity);
        }
        _dbSet.Remove(entity);
    }

    public virtual void DeleteRange(IEnumerable<T> entities)
    {
        _dbSet.RemoveRange(entities);
    }

    public virtual async Task DeleteByIdAsync(int id, CancellationToken cancellationToken = default)
    {
        var entity = await GetByIdAsync(id, cancellationToken);
        if (entity != null)
        {
            Delete(entity);
        }
    }

    /// <summary>
    /// Soft delete by ID without loading the entity
    /// </summary>
    public virtual async Task<bool> SoftDeleteByIdAsync(int id, CancellationToken cancellationToken = default)
    {
        var rowsAffected = await _dbSet
            .Where(x => x.Id == id && !x.IsDeleted)
            .ExecuteUpdateAsync(x => x
                .SetProperty(e => e.IsDeleted, true)
                .SetProperty(e => e.DeletedAt, DateTime.UtcNow),
                cancellationToken);

        return rowsAffected > 0;
    }

    /// <summary>
    /// Bulk delete entities matching the specification
    /// </summary>
    public virtual async Task<int> BulkDeleteAsync(ISpecification<T> spec, CancellationToken cancellationToken = default)
    {
        var query = _dbSet.AsQueryable();

        if (spec.Criteria != null)
        {
            query = query.Where(spec.Criteria);
        }

        return await query.ExecuteDeleteAsync(cancellationToken);
    }

    protected virtual IQueryable<T> ApplySpecification(ISpecification<T> spec)
    {
        return SpecificationEvaluator.GetQuery(_dbSet.AsQueryable(), spec);
    }
}
