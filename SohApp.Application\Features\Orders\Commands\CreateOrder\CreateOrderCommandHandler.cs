using MediatR;
using Microsoft.Extensions.Logging;
using SohApp.Application.Common;
using SohApp.Core.Entities;
using SohApp.Core.Events.OrderEvents;
using SohApp.Core.Exceptions;
using SohApp.Core.Interfaces;
using SohApp.Core.ValueObjects;

namespace SohApp.Application.Features.Orders.Commands.CreateOrder;

public class CreateOrderCommandHandler : IRequestHandler<CreateOrderCommand, Result<int>>
{
    private readonly IUnitOfWork _unitOfWork;
    private readonly ILogger<CreateOrderCommandHandler> _logger;

    public CreateOrderCommandHandler(
        IUnitOfWork unitOfWork,
        ILogger<CreateOrderCommandHandler> logger)
    {
        _unitOfWork = unitOfWork;
        _logger = logger;
    }

    public async Task<Result<int>> Handle(CreateOrderCommand request, CancellationToken cancellationToken)
    {
        try
        {
            // Validate user exists
            var user = await _unitOfWork.Users.GetByIdAsync(request.UserId, cancellationToken);
            if (user == null)
            {
                return Result<int>.Failure("User not found");
            }

            if (!user.IsActive)
            {
                return Result<int>.Failure("User account is not active");
            }

            // Validate items
            if (!request.Items.Any())
            {
                return Result<int>.Failure("Order must contain at least one item");
            }

            // Get all products for the order items
            var productIds = request.Items.Select(i => i.ProductId).Distinct().ToList();
            var products = new List<Product>();
            
            foreach (var productId in productIds)
            {
                var product = await _unitOfWork.Products.GetByIdAsync(productId, cancellationToken);
                if (product == null)
                {
                    return Result<int>.Failure($"Product with ID {productId} not found");
                }
                
                if (!product.IsActive)
                {
                    return Result<int>.Failure($"Product '{product.Name}' is not available");
                }
                
                products.Add(product);
            }

            // Validate stock availability
            foreach (var item in request.Items)
            {
                var product = products.First(p => p.Id == item.ProductId);
                if (product.StockQuantity < item.Quantity)
                {
                    return Result<int>.Failure($"Insufficient stock for product '{product.Name}'. Available: {product.StockQuantity}, Requested: {item.Quantity}");
                }
            }

            // Generate order number
            var orderNumber = await _unitOfWork.Orders.GenerateOrderNumberAsync(cancellationToken);

            // Create order
            var order = new Order
            {
                UserId = request.UserId,
                OrderNumber = orderNumber
                // TotalAmount will be calculated automatically when adding items
            };

            // Add order items
            foreach (var item in request.Items)
            {
                var product = products.First(p => p.Id == item.ProductId);
                order.AddItem(product, item.Quantity);
                
                // Update product stock
                product.UpdateStock(-item.Quantity);
                _unitOfWork.Products.Update(product);
            }

            // Add domain event
            order.AddDomainEvent(new OrderCreatedEvent(order.Id, order.UserId, order.TotalAmount.Amount));

            // Use execution strategy-aware transaction for data consistency
            var orderId = await _unitOfWork.ExecuteInTransactionAsync(async () =>
            {
                // Save order without dispatching domain events (to avoid execution strategy conflicts)
                await _unitOfWork.Orders.AddAsync(order, cancellationToken);
                await _unitOfWork.SaveChangesWithoutEventsAsync(cancellationToken);

                _logger.LogInformation("Order created successfully with ID: {OrderId} and Order Number: {OrderNumber}",
                    order.Id, order.OrderNumber);

                return order.Id;
            }, cancellationToken);

            // Dispatch domain events after the transaction is complete
            try
            {
                await _unitOfWork.PublishDomainEventsAsync(cancellationToken);
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Failed to dispatch domain events for order {OrderId}", orderId);
                // Don't fail the operation if domain events fail - they can be retried
            }

            return Result<int>.Success(orderId);
        }
        catch (BusinessRuleException ex)
        {
            _logger.LogWarning(ex, "Business rule violation while creating order for user {UserId}", request.UserId);
            return Result<int>.Failure(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating order for user {UserId}", request.UserId);
            return Result<int>.Failure("An error occurred while creating the order");
        }
    }
}
