using SohApp.Core.Entities;
using SohApp.Core.Specifications.Base;

namespace SohApp.Core.Specifications.ProductSpecifications;

public class ProductsByCategorySpecification : BaseSpecification<Product>
{
    public ProductsByCategorySpecification(int categoryId) : base(p => p.CategoryId == categoryId && p.IsActive)
    {
        AddInclude(p => p.Category);
        ApplyOrderBy(p => p.Name);
    }
}
