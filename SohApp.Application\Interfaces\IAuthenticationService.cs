using SohApp.Application.Common;
using SohApp.Application.DTOs.Auth;

namespace SohApp.Application.Interfaces;

public interface IAuthenticationService
{
    /// <summary>
    /// Authenticates a user with email and password
    /// </summary>
    /// <param name="loginDto">Login credentials</param>
    /// <param name="ipAddress">Client IP address</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Login response with tokens</returns>
    Task<Result<LoginResponseDto>> LoginAsync(LoginDto loginDto, string? ipAddress = null, CancellationToken cancellationToken = default);

    /// <summary>
    /// Refreshes access token using refresh token
    /// </summary>
    /// <param name="refreshTokenDto">Refresh token</param>
    /// <param name="ipAddress">Client IP address</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>New tokens</returns>
    Task<Result<RefreshTokenResponseDto>> RefreshTokenAsync(RefreshTokenDto refreshTokenDto, string? ipAddress = null, CancellationToken cancellationToken = default);

    /// <summary>
    /// Revokes a refresh token
    /// </summary>
    /// <param name="refreshTokenDto">Refresh token to revoke</param>
    /// <param name="ipAddress">Client IP address</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Success result</returns>
    Task<Result> RevokeTokenAsync(RefreshTokenDto refreshTokenDto, string? ipAddress = null, CancellationToken cancellationToken = default);

    /// <summary>
    /// Revokes all refresh tokens for a user
    /// </summary>
    /// <param name="userId">User ID</param>
    /// <param name="ipAddress">Client IP address</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Success result</returns>
    Task<Result> RevokeAllTokensAsync(int userId, string? ipAddress = null, CancellationToken cancellationToken = default);
}
