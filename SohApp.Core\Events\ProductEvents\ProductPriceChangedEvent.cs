using SohApp.Core.Events.Base;
using SohApp.Core.ValueObjects;

namespace SohApp.Core.Events.ProductEvents;

public class ProductPriceChangedEvent : DomainEvent
{
    public int ProductId { get; }
    public Money OldPrice { get; }
    public Money NewPrice { get; }

    public ProductPriceChangedEvent(int productId, Money oldPrice, Money newPrice)
    {
        ProductId = productId;
        OldPrice = oldPrice;
        NewPrice = newPrice;
    }
}
