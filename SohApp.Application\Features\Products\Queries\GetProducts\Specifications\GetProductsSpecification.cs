using System.Linq.Expressions;
using SohApp.Core.Entities;
using SohApp.Core.Specifications.Base;

namespace SohApp.Application.Features.Products.Queries.GetProducts.Specifications;

public class GetProductsSpecification : BaseSpecification<Product>
{
    public GetProductsSpecification(
        int? categoryId = null,
        string? searchTerm = null,
        decimal? minPrice = null,
        decimal? maxPrice = null,
        bool? inStock = null,
        string? sortBy = null,
        bool sortDescending = false)
    {
        // Build criteria using proper expression composition
        var criteria = BuildCriteria(categoryId, searchTerm, minPrice, maxPrice, inStock);
        SetCriteria(criteria);

        // Apply sorting and includes
        ApplySortingAndIncludes(sortBy, sortDescending);
    }

    private static Expression<Func<Product, bool>> BuildCriteria(
        int? categoryId,
        string? searchTerm,
        decimal? minPrice,
        decimal? maxPrice,
        bool? inStock)
    {
        Expression<Func<Product, bool>> criteria = p => p.IsActive && !p.IsDeleted;

        if (categoryId.HasValue)
        {
            var categoryFilter = CreateExpression<Product>(p => p.CategoryId == categoryId.Value);
            criteria = CombineExpressions(criteria, categoryFilter, ExpressionType.AndAlso);
        }

        if (!string.IsNullOrWhiteSpace(searchTerm))
        {
            var searchLower = searchTerm.ToLowerInvariant();
            var searchFilter = CreateExpression<Product>(p =>
                p.Name.ToLower().Contains(searchLower) ||
                p.Description.ToLower().Contains(searchLower) ||
                p.Sku.ToLower().Contains(searchLower));
            criteria = CombineExpressions(criteria, searchFilter, ExpressionType.AndAlso);
        }

        if (minPrice.HasValue)
        {
            var minPriceFilter = CreateExpression<Product>(p => p.Price.Amount >= minPrice.Value);
            criteria = CombineExpressions(criteria, minPriceFilter, ExpressionType.AndAlso);
        }

        if (maxPrice.HasValue)
        {
            var maxPriceFilter = CreateExpression<Product>(p => p.Price.Amount <= maxPrice.Value);
            criteria = CombineExpressions(criteria, maxPriceFilter, ExpressionType.AndAlso);
        }

        if (inStock.HasValue && inStock.Value)
        {
            var stockFilter = CreateExpression<Product>(p => p.StockQuantity > 0);
            criteria = CombineExpressions(criteria, stockFilter, ExpressionType.AndAlso);
        }

        return criteria;
    }

    private void ApplySortingAndIncludes(string? sortBy, bool sortDescending)
    {
        // Include category for mapping
        AddInclude(p => p.Category);

        // Apply sorting
        switch (sortBy?.ToLowerInvariant())
        {
            case "name":
                if (sortDescending)
                    ApplyOrderByDescending(p => p.Name);
                else
                    ApplyOrderBy(p => p.Name);
                break;
            case "price":
                if (sortDescending)
                    ApplyOrderByDescending(p => p.Price.Amount);
                else
                    ApplyOrderBy(p => p.Price.Amount);
                break;
            case "stock":
                if (sortDescending)
                    ApplyOrderByDescending(p => p.StockQuantity);
                else
                    ApplyOrderBy(p => p.StockQuantity);
                break;
            case "createdat":
                if (sortDescending)
                    ApplyOrderByDescending(p => p.CreatedAt);
                else
                    ApplyOrderBy(p => p.CreatedAt);
                break;
            default:
                ApplyOrderBy(p => p.Name);
                break;
        }

        EnableNoTracking();
    }

    private static Expression<Func<T, bool>> CreateExpression<T>(Expression<Func<T, bool>> expression)
    {
        return expression;
    }

    private static Expression<Func<T, bool>> CombineExpressions<T>(
        Expression<Func<T, bool>> first,
        Expression<Func<T, bool>> second,
        ExpressionType mergeWith)
    {
        var parameter = Expression.Parameter(typeof(T));

        var leftVisitor = new ReplaceExpressionVisitor(first.Parameters[0], parameter);
        var left = leftVisitor.Visit(first.Body);

        var rightVisitor = new ReplaceExpressionVisitor(second.Parameters[0], parameter);
        var right = rightVisitor.Visit(second.Body);

        var body = mergeWith == ExpressionType.AndAlso
            ? Expression.AndAlso(left!, right!)
            : Expression.OrElse(left!, right!);

        return Expression.Lambda<Func<T, bool>>(body, parameter);
    }

    private class ReplaceExpressionVisitor : ExpressionVisitor
    {
        private readonly Expression _oldValue;
        private readonly Expression _newValue;

        public ReplaceExpressionVisitor(Expression oldValue, Expression newValue)
        {
            _oldValue = oldValue;
            _newValue = newValue;
        }

        public override Expression? Visit(Expression? node)
        {
            return node == _oldValue ? _newValue : base.Visit(node);
        }
    }
}
