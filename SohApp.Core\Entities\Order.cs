using SohApp.Core.Constants;
using SohApp.Core.Entities.Base;
using SohApp.Core.Enums;
using SohApp.Core.Events.OrderEvents;
using SohApp.Core.Exceptions;
using SohApp.Core.ValueObjects;

namespace SohApp.Core.Entities;

public class Order : BaseEntity
{
    private readonly List<OrderItem> _orderItems = new();
    private string _orderNumber = string.Empty;

    public int UserId { get; set; }

    public string OrderNumber
    {
        get => _orderNumber;
        set => _orderNumber = ValidateOrderNumber(value);
    }

    public OrderStatus Status { get; private set; } = OrderStatus.Pending;
    public Money TotalAmount { get; private set; } = null!;
    public DateTime? ShippedAt { get; private set; }
    public DateTime? DeliveredAt { get; private set; }

    // Navigation properties - removed virtual to prevent lazy loading issues
    public User User { get; set; } = null!;
    public IReadOnlyCollection<OrderItem> OrderItems => _orderItems.AsReadOnly();

    // Computed properties
    public bool CanBeModified => Status == OrderStatus.Pending;
    public bool CanBeCancelled => Status != OrderStatus.Delivered && Status != OrderStatus.Cancelled;
    public bool CanBeShipped => Status == OrderStatus.Confirmed || Status == OrderStatus.Processing;
    public int ItemCount => _orderItems.Count;
    public int TotalQuantity => _orderItems.Sum(x => x.Quantity);

    /// <summary>
    /// Adds an item to the order or updates quantity if item already exists
    /// </summary>
    /// <param name="product">The product to add</param>
    /// <param name="quantity">The quantity to add</param>
    /// <exception cref="ArgumentNullException">Thrown when product is null</exception>
    /// <exception cref="ArgumentException">Thrown when quantity is invalid</exception>
    /// <exception cref="BusinessRuleException">Thrown when order cannot be modified</exception>
    public void AddItem(Product product, int quantity)
    {
        ArgumentNullException.ThrowIfNull(product);

        if (quantity <= 0)
            throw new ArgumentException("Quantity must be positive", nameof(quantity));

        if (!CanBeModified)
            throw new BusinessRuleException("Cannot modify order in current status");

        if (!product.IsActive)
            throw new BusinessRuleException("Cannot add inactive product to order");

        // Check if product has sufficient stock
        if (product.StockQuantity < quantity)
            throw new BusinessRuleException($"Insufficient stock. Available: {product.StockQuantity}, Requested: {quantity}");

        var existingItem = _orderItems.FirstOrDefault(x => x.ProductId == product.Id);
        if (existingItem != null)
        {
            var newQuantity = existingItem.Quantity + quantity;
            if (product.StockQuantity < newQuantity)
                throw new BusinessRuleException($"Insufficient stock for total quantity. Available: {product.StockQuantity}, Total requested: {newQuantity}");

            existingItem.UpdateQuantity(newQuantity);
        }
        else
        {
            var orderItem = new OrderItem
            {
                ProductId = product.Id,
                Product = product,
                Quantity = quantity,
                UnitPrice = product.Price,
                TotalPrice = new Money(product.Price.Amount * quantity, product.Price.Currency)
            };
            _orderItems.Add(orderItem);
        }

        RecalculateTotal();
        AddDomainEvent(new OrderItemAddedEvent(Id, product.Id, quantity));
    }

    /// <summary>
    /// Removes an item from the order
    /// </summary>
    /// <param name="productId">The product ID to remove</param>
    /// <exception cref="BusinessRuleException">Thrown when order cannot be modified or item not found</exception>
    public void RemoveItem(int productId)
    {
        if (!CanBeModified)
            throw new BusinessRuleException("Cannot modify order in current status");

        var item = _orderItems.FirstOrDefault(x => x.ProductId == productId);
        if (item == null)
            throw new BusinessRuleException("Item not found in order");

        _orderItems.Remove(item);
        RecalculateTotal();
        AddDomainEvent(new OrderItemRemovedEvent(Id, productId));
    }

    /// <summary>
    /// Updates the quantity of an existing item
    /// </summary>
    /// <param name="productId">The product ID to update</param>
    /// <param name="newQuantity">The new quantity</param>
    /// <exception cref="ArgumentException">Thrown when quantity is invalid</exception>
    /// <exception cref="BusinessRuleException">Thrown when order cannot be modified or item not found</exception>
    public void UpdateItemQuantity(int productId, int newQuantity)
    {
        if (newQuantity <= 0)
            throw new ArgumentException("Quantity must be positive", nameof(newQuantity));

        if (!CanBeModified)
            throw new BusinessRuleException("Cannot modify order in current status");

        var item = _orderItems.FirstOrDefault(x => x.ProductId == productId);
        if (item == null)
            throw new BusinessRuleException("Item not found in order");

        // Check stock availability
        if (item.Product.StockQuantity < newQuantity)
            throw new BusinessRuleException($"Insufficient stock. Available: {item.Product.StockQuantity}, Requested: {newQuantity}");

        item.UpdateQuantity(newQuantity);
        RecalculateTotal();
        AddDomainEvent(new OrderItemQuantityUpdatedEvent(Id, productId, newQuantity));
    }

    /// <summary>
    /// Confirms the order and reserves stock for all items
    /// </summary>
    /// <exception cref="BusinessRuleException">Thrown when order cannot be confirmed</exception>
    public void Confirm()
    {
        if (Status != OrderStatus.Pending)
            throw new BusinessRuleException("Order is already confirmed");

        if (!_orderItems.Any())
            throw new BusinessRuleException("Cannot confirm empty order");

        // Validate stock availability for all items
        foreach (var item in _orderItems)
        {
            if (item.Product.StockQuantity < item.Quantity)
                throw new BusinessRuleException($"Insufficient stock for product {item.Product.Name}. Available: {item.Product.StockQuantity}, Required: {item.Quantity}");
        }

        // Reserve stock for all items
        foreach (var item in _orderItems)
        {
            item.Product.ReserveStock(item.Quantity);
        }

        Status = OrderStatus.Confirmed;
        AddDomainEvent(new OrderConfirmedEvent(Id, UserId, TotalAmount.Amount));
    }

    /// <summary>
    /// Marks the order as processing
    /// </summary>
    /// <exception cref="BusinessRuleException">Thrown when order cannot be processed</exception>
    public void StartProcessing()
    {
        if (Status != OrderStatus.Confirmed)
            throw new BusinessRuleException("Only confirmed orders can be processed");

        Status = OrderStatus.Processing;
        AddDomainEvent(new OrderProcessingStartedEvent(Id, UserId));
    }

    /// <summary>
    /// Ships the order
    /// </summary>
    /// <param name="shippedAt">The shipping timestamp (defaults to current UTC time if not provided)</param>
    /// <exception cref="BusinessRuleException">Thrown when order cannot be shipped</exception>
    public void Ship(DateTime? shippedAt = null)
    {
        if (!CanBeShipped)
            throw new BusinessRuleException("Order must be confirmed or processing to ship");

        Status = OrderStatus.Shipped;
        ShippedAt = shippedAt ?? DateTime.UtcNow;
        AddDomainEvent(new OrderShippedEvent(Id, UserId, ShippedAt.Value));
    }

    /// <summary>
    /// Marks the order as delivered
    /// </summary>
    /// <param name="deliveredAt">The delivery timestamp (defaults to current UTC time if not provided)</param>
    /// <exception cref="BusinessRuleException">Thrown when order cannot be delivered</exception>
    public void Deliver(DateTime? deliveredAt = null)
    {
        if (Status != OrderStatus.Shipped)
            throw new BusinessRuleException("Only shipped orders can be delivered");

        Status = OrderStatus.Delivered;
        DeliveredAt = deliveredAt ?? DateTime.UtcNow;
        AddDomainEvent(new OrderDeliveredEvent(Id, UserId, DeliveredAt.Value));
    }

    /// <summary>
    /// Cancels the order and releases reserved stock
    /// </summary>
    /// <exception cref="BusinessRuleException">Thrown when order cannot be cancelled</exception>
    public void Cancel()
    {
        if (!CanBeCancelled)
            throw new BusinessRuleException("Cannot cancel delivered or already cancelled order");

        // Release reserved stock if order was confirmed
        if (Status == OrderStatus.Confirmed || Status == OrderStatus.Processing)
        {
            foreach (var item in _orderItems)
            {
                item.Product.UpdateStock(item.Quantity); // Add stock back
            }
        }

        Status = OrderStatus.Cancelled;
        AddDomainEvent(new OrderCancelledEvent(Id, UserId));
    }

    /// <summary>
    /// Recalculates the total amount based on current order items
    /// </summary>
    private void RecalculateTotal()
    {
        if (!_orderItems.Any())
        {
            TotalAmount = new Money(0, "USD"); // Default currency - should be configurable
            return;
        }

        // Use the currency from the first item (assuming all items have the same currency)
        var currency = _orderItems.First().TotalPrice.Currency;
        var total = _orderItems.Sum(x => x.TotalPrice.Amount);
        TotalAmount = new Money(total, currency);
    }

    /// <summary>
    /// Validates the order number format: ORD + YYMMDD + NNN (12 characters total)
    /// </summary>
    /// <param name="orderNumber">The order number to validate</param>
    /// <returns>The validated order number</returns>
    /// <exception cref="ArgumentException">Thrown when order number is invalid</exception>
    private static string ValidateOrderNumber(string orderNumber)
    {
        if (string.IsNullOrWhiteSpace(orderNumber))
            throw new ArgumentException("Order number cannot be empty", nameof(orderNumber));

        if (orderNumber.Length != DomainConstants.Order.OrderNumberLength)
            throw new ArgumentException(
                $"Order number must be exactly {DomainConstants.Order.OrderNumberLength} characters. " +
                $"Expected format: {DomainConstants.Order.OrderNumberPrefix}YYMMDDNNN (e.g., ORD240315001). " +
                $"Received: '{orderNumber}' ({orderNumber.Length} characters)",
                nameof(orderNumber));

        if (!orderNumber.StartsWith(DomainConstants.Order.OrderNumberPrefix))
            throw new ArgumentException(
                $"Order number must start with '{DomainConstants.Order.OrderNumberPrefix}'. " +
                $"Expected format: {DomainConstants.Order.OrderNumberPrefix}YYMMDDNNN (e.g., ORD240315001). " +
                $"Received: '{orderNumber}'",
                nameof(orderNumber));

        // Validate the date part (positions 3-8: YYMMDD)
        var datePart = orderNumber.Substring(3, 6);
        if (!IsValidDateFormat(datePart))
            throw new ArgumentException(
                $"Order number contains invalid date format. " +
                $"Expected format: {DomainConstants.Order.OrderNumberPrefix}YYMMDDNNN where YYMMDD is a valid date. " +
                $"Received: '{orderNumber}' with date part '{datePart}'",
                nameof(orderNumber));

        // Validate the sequence part (positions 9-11: NNN)
        var sequencePart = orderNumber.Substring(9, 3);
        if (!int.TryParse(sequencePart, out var sequence) || sequence < 1 || sequence > DomainConstants.Order.MaxOrdersPerDay)
            throw new ArgumentException(
                $"Order number contains invalid sequence. " +
                $"Expected format: {DomainConstants.Order.OrderNumberPrefix}YYMMDDNNN where NNN is 001-{DomainConstants.Order.MaxOrdersPerDay:D3}. " +
                $"Received: '{orderNumber}' with sequence part '{sequencePart}'",
                nameof(orderNumber));

        return orderNumber.ToUpperInvariant();
    }

    /// <summary>
    /// Validates if the date part (YYMMDD) represents a valid date
    /// </summary>
    /// <param name="datePart">The 6-character date part (YYMMDD)</param>
    /// <returns>True if valid date format</returns>
    private static bool IsValidDateFormat(string datePart)
    {
        if (datePart.Length != 6)
            return false;

        if (!int.TryParse(datePart.Substring(0, 2), out var year) ||
            !int.TryParse(datePart.Substring(2, 2), out var month) ||
            !int.TryParse(datePart.Substring(4, 2), out var day))
            return false;

        // Convert 2-digit year to 4-digit year (assuming 20xx)
        var fullYear = 2000 + year;

        try
        {
            var date = new DateTime(fullYear, month, day);
            return true;
        }
        catch
        {
            return false;
        }
    }
}
