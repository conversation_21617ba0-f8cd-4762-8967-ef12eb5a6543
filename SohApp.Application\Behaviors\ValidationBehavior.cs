using FluentValidation;
using MediatR;
using Microsoft.Extensions.Logging;

namespace SohApp.Application.Behaviors;

public class ValidationBehavior<TRequest, TResponse> : IPipelineBehavior<TRequest, TResponse>
    where TRequest : IRequest<TResponse>
{
    private readonly IEnumerable<IValidator<TRequest>> _validators;
    private readonly ILogger<ValidationBehavior<TRequest, TResponse>> _logger;

    public ValidationBehavior(
        IEnumerable<IValidator<TRequest>> validators,
        ILogger<ValidationBehavior<TRequest, TResponse>> logger)
    {
        _validators = validators;
        _logger = logger;
    }

    public async Task<TResponse> Handle(TRequest request, RequestHandlerDelegate<TResponse> next, CancellationToken cancellationToken)
    {
        // Early return if no validators - avoid unnecessary allocations
        if (!_validators.Any())
        {
            return await next();
        }

        var context = new ValidationContext<TRequest>(request);

        // Optimize: Use List instead of LINQ for better performance
        var failures = new List<string>();

        // Validate sequentially to avoid creating unnecessary tasks for simple validations
        foreach (var validator in _validators)
        {
            var validationResult = await validator.ValidateAsync(context, cancellationToken);

            if (!validationResult.IsValid)
            {
                foreach (var error in validationResult.Errors)
                {
                    if (error != null)
                    {
                        failures.Add(error.ErrorMessage);
                    }
                }
            }
        }

        if (failures.Count > 0)
        {
            _logger.LogWarning("Validation failed for {RequestType} with {ErrorCount} errors: {Errors}",
                typeof(TRequest).Name,
                failures.Count,
                string.Join(", ", failures));

            throw new Core.Exceptions.ValidationException(failures);
        }

        return await next();
    }
}
