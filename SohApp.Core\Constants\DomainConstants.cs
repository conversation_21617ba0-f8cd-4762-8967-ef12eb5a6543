namespace SohApp.Core.Constants;

public static class DomainConstants
{
    public static class User
    {
        public const int NameMaxLength = 100; // Combined constant for both first and last name
        public const int FirstNameMaxLength = NameMaxLength;
        public const int LastNameMaxLength = NameMaxLength;
        public const int EmailMaxLength = 255;
        public const int PasswordMinLength = 8;
        public const int PasswordMaxLength = 128;
    }

    public static class Product
    {
        public const int NameMaxLength = 200;
        public const int DescriptionMaxLength = 1000;
        public const int SkuMaxLength = 50;
        public const int LowStockThreshold = 10;
        public const int MaxStockQuantity = 999999;
    }
    
    public static class Order
    {
        /// <summary>
        /// Total length of order number: ORD (3) + YYMMDD (6) + NNN (3) = 12 characters
        /// </summary>
        public const int OrderNumberLength = 12;

        /// <summary>
        /// Order number prefix: "ORD"
        /// </summary>
        public const string OrderNumberPrefix = "ORD";

        /// <summary>
        /// Maximum orders per day (sequence 001-999)
        /// </summary>
        public const int MaxOrdersPerDay = 999;

        /// <summary>
        /// Length of the sequence part (3 digits)
        /// </summary>
        public const int SequenceLength = 3;
    }
    
    public static class Category
    {
        public const int NameMaxLength = 100;
        public const int DescriptionMaxLength = 500;
    }
}
