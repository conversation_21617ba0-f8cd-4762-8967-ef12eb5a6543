namespace SohApp.Application.Common;

public class PagedResult<T>
{
    public IReadOnlyList<T> Data { get; set; } = new List<T>();
    public int TotalCount { get; set; }
    public int Page { get; set; }
    public int PageSize { get; set; }
    public int TotalPages { get; set; }
    public bool HasPreviousPage { get; set; }
    public bool HasNextPage { get; set; }
    public int? PreviousPage => HasPreviousPage ? Page - 1 : null;
    public int? NextPage => HasNextPage ? Page + 1 : null;
    public int StartIndex => (Page - 1) * PageSize + 1;
    public int EndIndex => Math.Min(StartIndex + PageSize - 1, TotalCount);
    public bool IsEmpty => !Data.Any();
    public bool IsFirstPage => Page == 1;
    public bool IsLastPage => Page == TotalPages;
}
