using FluentValidation;
using SohApp.Core.Constants;

namespace SohApp.Application.Features.Products.Commands.CreateProduct;

public class CreateProductCommandValidator : AbstractValidator<CreateProductCommand>
{
    public CreateProductCommandValidator()
    {
        RuleFor(x => x.Name)
            .NotEmpty().WithMessage("Product name is required")
            .MaximumLength(DomainConstants.Product.NameMaxLength).WithMessage($"Product name must not exceed {DomainConstants.Product.NameMaxLength} characters");

        RuleFor(x => x.Description)
            .MaximumLength(DomainConstants.Product.DescriptionMaxLength).WithMessage($"Description must not exceed {DomainConstants.Product.DescriptionMaxLength} characters");

        RuleFor(x => x.Sku)
            .NotEmpty().WithMessage("SKU is required")
            .MaximumLength(DomainConstants.Product.SkuMaxLength).WithMessage($"SKU must not exceed {DomainConstants.Product.SkuMaxLength} characters");

        RuleFor(x => x.Price)
            .GreaterThan(0).WithMessage("Price must be greater than zero");

        RuleFor(x => x.Currency)
            .NotEmpty().WithMessage("Currency is required")
            .Length(3).WithMessage("Currency must be 3 characters long");

        RuleFor(x => x.StockQuantity)
            .GreaterThanOrEqualTo(0).WithMessage("Stock quantity cannot be negative");

        RuleFor(x => x.CategoryId)
            .GreaterThan(0).WithMessage("Category ID must be greater than zero");
    }
}
