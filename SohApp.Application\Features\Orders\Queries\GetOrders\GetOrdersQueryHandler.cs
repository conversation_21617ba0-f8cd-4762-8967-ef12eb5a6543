using AutoMapper;
using MediatR;
using Microsoft.Extensions.Logging;
using SohApp.Application.Common;
using SohApp.Application.DTOs.Order;
using SohApp.Application.Features.Orders.Queries.GetOrders.Specifications;
using SohApp.Core.Interfaces;

namespace SohApp.Application.Features.Orders.Queries.GetOrders;

public class GetOrdersQueryHandler : IRequestHandler<GetOrdersQuery, Result<PagedResult<OrderDto>>>
{
    private readonly IUnitOfWork _unitOfWork;
    private readonly IMapper _mapper;
    private readonly ILogger<GetOrdersQueryHandler> _logger;

    public GetOrdersQueryHandler(
        IUnitOfWork unitOfWork,
        IMapper mapper,
        ILogger<GetOrdersQueryHandler> logger)
    {
        _unitOfWork = unitOfWork;
        _mapper = mapper;
        _logger = logger;
    }

    public async Task<Result<PagedResult<OrderDto>>> Handle(GetOrdersQuery request, CancellationToken cancellationToken)
    {
        try
        {
            var spec = new GetOrdersSpecification(
                request.UserId,
                request.Status,
                request.StartDate,
                request.EndDate,
                request.SearchTerm,
                request.SortBy,
                request.SortDescending);
            
            var totalCount = await _unitOfWork.Orders.CountAsync(spec, cancellationToken);
            
            // Apply pagination
            spec.ApplyPaging((request.Page - 1) * request.PageSize, request.PageSize);
            
            var orders = await _unitOfWork.Orders.GetAsync(spec, cancellationToken);
            var orderDtos = _mapper.Map<IReadOnlyList<OrderDto>>(orders);
            
            var result = new PagedResult<OrderDto>
            {
                Data = orderDtos,
                TotalCount = totalCount,
                Page = request.Page,
                PageSize = request.PageSize,
                TotalPages = (int)Math.Ceiling(totalCount / (double)request.PageSize)
            };

            return Result<PagedResult<OrderDto>>.Success(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving orders");
            return Result<PagedResult<OrderDto>>.Failure("An error occurred while retrieving orders");
        }
    }
}
