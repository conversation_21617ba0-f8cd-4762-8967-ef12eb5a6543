using MediatR;
using Microsoft.Extensions.Logging;
using SohApp.Application.Common;
using SohApp.Application.Interfaces;
using SohApp.Core.Entities;
using SohApp.Core.Events.UserEvents;
using SohApp.Core.Interfaces;
using SohApp.Core.ValueObjects;

namespace SohApp.Application.Features.Users.Commands.CreateUser;

public class CreateUserCommandHandler : IRequestHandler<CreateUserCommand, Result<int>>
{
    private readonly IUnitOfWork _unitOfWork;
    private readonly IPasswordHasher _passwordHasher;
    private readonly ILogger<CreateUserCommandHandler> _logger;

    public CreateUserCommandHandler(
        IUnitOfWork unitOfWork,
        IPasswordHasher passwordHasher,
        ILogger<CreateUserCommandHandler> logger)
    {
        _unitOfWork = unitOfWork;
        _passwordHasher = passwordHasher;
        _logger = logger;
    }

    public async Task<Result<int>> Handle(CreateUserCommand request, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogDebug("Creating user with email: {Email}", request.Email);

            // Validate and create Email value object
            Email email;
            try
            {
                email = new Email(request.Email);
            }
            catch (ArgumentException ex)
            {
                _logger.LogWarning("Invalid email format provided: {Email}", request.Email);
                return Result<int>.Failure($"Invalid email format: {ex.Message}");
            }

            // Check if email is unique
            var isEmailUnique = await _unitOfWork.Users.IsEmailUniqueAsync(request.Email, cancellationToken: cancellationToken);
            if (!isEmailUnique)
            {
                _logger.LogWarning("Attempt to create user with existing email: {Email}", request.Email);
                return Result<int>.Failure("A user with this email address already exists");
            }

            // Hash password
            string passwordHash;
            try
            {
                passwordHash = _passwordHasher.HashPassword(request.Password);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error hashing password for user: {Email}", request.Email);
                return Result<int>.Failure("An error occurred while processing the password");
            }

            // Create user entity using proper constructor/properties
            var user = new User
            {
                FirstName = request.FirstName,
                LastName = request.LastName,
                Email = email,
                PasswordHash = passwordHash,
                Role = request.Role,
                IsActive = true,
                CreatedAt = DateTime.UtcNow,
                CreatedBy = "System" // TODO: Get from current user context
            };

            // Add domain event - this will be dispatched after successful save
            user.AddDomainEvent(new UserCreatedEvent(user.Id, email.Value, user.FullName));

            // Use execution strategy-aware transaction for data consistency
            var userId = await _unitOfWork.ExecuteInTransactionAsync(async () =>
            {
                // Save to database without dispatching domain events (to avoid execution strategy conflicts)
                await _unitOfWork.Users.AddAsync(user, cancellationToken);
                await _unitOfWork.SaveChangesWithoutEventsAsync(cancellationToken);

                _logger.LogInformation("User created successfully with ID: {UserId} and email: {Email}", user.Id, email.Value);

                return user.Id;
            }, cancellationToken);

            // Dispatch domain events after the transaction is complete
            try
            {
                await _unitOfWork.PublishDomainEventsAsync(cancellationToken);
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Failed to dispatch domain events for user {UserId}", userId);
                // Don't fail the operation if domain events fail - they can be retried
            }

            return Result<int>.Success(userId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Unexpected error creating user with email: {Email}", request.Email);
            return Result<int>.Failure("An unexpected error occurred while creating the user. Please try again.");
        }
    }
}
