using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using SohApp.Core.ValueObjects;

namespace SohApp.Infrastructure.Data.Converters;

/// <summary>
/// Value converter for Email value object to enable proper EF Core translation
/// </summary>
public class EmailConverter : ValueConverter<Email, string>
{
    public EmailConverter() : base(
        email => email.Value,
        value => new Email(value))
    {
    }
}
