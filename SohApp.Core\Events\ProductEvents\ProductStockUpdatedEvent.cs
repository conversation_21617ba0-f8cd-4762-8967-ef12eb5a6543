using SohApp.Core.Events.Base;

namespace SohApp.Core.Events.ProductEvents;

public class ProductStockUpdatedEvent : DomainEvent
{
    public int ProductId { get; }
    public int OldStockQuantity { get; }
    public int NewStockQuantity { get; }
    public int StockChange => NewStockQuantity - OldStockQuantity;

    public ProductStockUpdatedEvent(int productId, int oldStockQuantity, int newStockQuantity)
    {
        ProductId = productId;
        OldStockQuantity = oldStockQuantity;
        NewStockQuantity = newStockQuantity;
    }
}
