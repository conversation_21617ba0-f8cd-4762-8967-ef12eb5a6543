using Microsoft.Extensions.Logging;
using SohApp.Application.Common;
using SohApp.Application.DTOs.Auth;
using SohApp.Application.Interfaces;
using SohApp.Core.Entities;
using SohApp.Core.Interfaces;
using SohApp.Core.Interfaces.Services;
using System.Security.Claims;

namespace SohApp.Infrastructure.Services;

public class AuthenticationService : IAuthenticationService
{
    private readonly IUnitOfWork _unitOfWork;
    private readonly IPasswordHasher _passwordHasher;
    private readonly IJwtService _jwtService;
    private readonly ILogger<AuthenticationService> _logger;

    public AuthenticationService(
        IUnitOfWork unitOfWork,
        IPasswordHasher passwordHasher,
        IJwtService jwtService,
        ILogger<AuthenticationService> logger)
    {
        _unitOfWork = unitOfWork;
        _passwordHasher = passwordHasher;
        _jwtService = jwtService;
        _logger = logger;
    }

    public async Task<Result<LoginResponseDto>> LoginAsync(LoginDto loginDto, string? ipAddress = null, CancellationToken cancellationToken = default)
    {
        try
        {
            // Find user by email
            var user = await _unitOfWork.Users.GetByEmailAsync(loginDto.Email, cancellationToken);
            if (user == null)
            {
                _logger.LogWarning("Login attempt with non-existent email: {Email}", loginDto.Email);
                return Result<LoginResponseDto>.Failure("Invalid email or password");
            }

            // Check if user is active
            if (!user.IsActive)
            {
                _logger.LogWarning("Login attempt for inactive user: {Email}", loginDto.Email);
                return Result<LoginResponseDto>.Failure("Account is not active");
            }

            // Verify password
            if (!_passwordHasher.VerifyPassword(user.PasswordHash, loginDto.Password))
            {
                _logger.LogWarning("Invalid password attempt for user: {Email}", loginDto.Email);
                return Result<LoginResponseDto>.Failure("Invalid email or password");
            }

            // Generate tokens
            var accessToken = _jwtService.GenerateAccessToken(user);
            var refreshToken = _jwtService.GenerateRefreshToken();
            var accessTokenExpiry = _jwtService.GetAccessTokenExpiryTime();
            var refreshTokenExpiry = _jwtService.GetRefreshTokenExpiryTime();

            // Create refresh token entity
            var refreshTokenEntity = new RefreshToken
            {
                Token = refreshToken,
                ExpiresAt = refreshTokenExpiry,
                UserId = user.Id
            };

            // Update user last login and add refresh token
            user.UpdateLastLogin();
            await _unitOfWork.RefreshTokens.AddAsync(refreshTokenEntity, cancellationToken);
            await _unitOfWork.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("User logged in successfully: {Email}", loginDto.Email);

            var response = new LoginResponseDto
            {
                AccessToken = accessToken,
                RefreshToken = refreshToken,
                AccessTokenExpiresAt = accessTokenExpiry,
                RefreshTokenExpiresAt = refreshTokenExpiry,
                User = new UserInfoDto
                {
                    Id = user.Id,
                    FirstName = user.FirstName,
                    LastName = user.LastName,
                    Email = user.Email.Value,
                    Role = user.Role.ToString(),
                    FullName = user.FullName
                }
            };

            return Result<LoginResponseDto>.Success(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during login for email: {Email}", loginDto.Email);
            return Result<LoginResponseDto>.Failure("An error occurred during login");
        }
    }

    public async Task<Result<RefreshTokenResponseDto>> RefreshTokenAsync(RefreshTokenDto refreshTokenDto, string? ipAddress = null, CancellationToken cancellationToken = default)
    {
        try
        {
            // Find refresh token
            var refreshToken = await _unitOfWork.RefreshTokens.GetByTokenAsync(refreshTokenDto.RefreshToken, cancellationToken);
            if (refreshToken == null)
            {
                _logger.LogWarning("Refresh token not found: {Token}", refreshTokenDto.RefreshToken);
                return Result<RefreshTokenResponseDto>.Failure("Invalid refresh token");
            }

            // Check if token is active
            if (!refreshToken.IsActive)
            {
                _logger.LogWarning("Inactive refresh token used: {Token}", refreshTokenDto.RefreshToken);
                return Result<RefreshTokenResponseDto>.Failure("Invalid refresh token");
            }

            // Get user
            var user = await _unitOfWork.Users.GetByIdAsync(refreshToken.UserId, cancellationToken);
            if (user == null || !user.IsActive)
            {
                _logger.LogWarning("User not found or inactive for refresh token: {UserId}", refreshToken.UserId);
                return Result<RefreshTokenResponseDto>.Failure("Invalid refresh token");
            }

            // Generate new tokens
            var newAccessToken = _jwtService.GenerateAccessToken(user);
            var newRefreshToken = _jwtService.GenerateRefreshToken();
            var accessTokenExpiry = _jwtService.GetAccessTokenExpiryTime();
            var refreshTokenExpiry = _jwtService.GetRefreshTokenExpiryTime();

            // Revoke old refresh token and create new one
            refreshToken.Revoke(ipAddress, "Replaced by new token", newRefreshToken);
            
            var newRefreshTokenEntity = new RefreshToken
            {
                Token = newRefreshToken,
                ExpiresAt = refreshTokenExpiry,
                UserId = user.Id
            };

            await _unitOfWork.RefreshTokens.AddAsync(newRefreshTokenEntity, cancellationToken);
            await _unitOfWork.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("Tokens refreshed successfully for user: {UserId}", user.Id);

            var response = new RefreshTokenResponseDto
            {
                AccessToken = newAccessToken,
                RefreshToken = newRefreshToken,
                AccessTokenExpiresAt = accessTokenExpiry,
                RefreshTokenExpiresAt = refreshTokenExpiry
            };

            return Result<RefreshTokenResponseDto>.Success(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during token refresh");
            return Result<RefreshTokenResponseDto>.Failure("An error occurred during token refresh");
        }
    }

    public async Task<Result> RevokeTokenAsync(RefreshTokenDto refreshTokenDto, string? ipAddress = null, CancellationToken cancellationToken = default)
    {
        try
        {
            var refreshToken = await _unitOfWork.RefreshTokens.GetByTokenAsync(refreshTokenDto.RefreshToken, cancellationToken);
            if (refreshToken == null)
            {
                _logger.LogWarning("Attempt to revoke non-existent refresh token");
                return Result.Failure("Invalid refresh token");
            }

            if (!refreshToken.IsActive)
            {
                _logger.LogWarning("Attempt to revoke already inactive refresh token");
                return Result.Failure("Token is already revoked");
            }

            refreshToken.Revoke(ipAddress, "Revoked by user");
            await _unitOfWork.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("Refresh token revoked successfully for user: {UserId}", refreshToken.UserId);
            return Result.Success();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during token revocation");
            return Result.Failure("An error occurred during token revocation");
        }
    }

    public async Task<Result> RevokeAllTokensAsync(int userId, string? ipAddress = null, CancellationToken cancellationToken = default)
    {
        try
        {
            var activeTokens = await _unitOfWork.RefreshTokens.GetActiveTokensByUserIdAsync(userId, cancellationToken);
            
            foreach (var token in activeTokens)
            {
                token.Revoke(ipAddress, "All tokens revoked by user");
            }

            await _unitOfWork.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("All refresh tokens revoked for user: {UserId}", userId);
            return Result.Success();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during revocation of all tokens for user: {UserId}", userId);
            return Result.Failure("An error occurred during token revocation");
        }
    }
}
