using SohApp.Core.Events.Base;

namespace SohApp.Core.Events.OrderEvents;

public class OrderConfirmedEvent : DomainEvent
{
    public int OrderId { get; }
    public int UserId { get; }
    public decimal TotalAmount { get; }

    public OrderConfirmedEvent(int orderId, int userId, decimal totalAmount)
    {
        OrderId = orderId;
        UserId = userId;
        TotalAmount = totalAmount;
    }
}
