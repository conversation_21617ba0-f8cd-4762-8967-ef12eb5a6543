using Microsoft.Extensions.Logging;
using SohApp.Core.Events.OrderEvents;
using SohApp.Core.Interfaces;
using SohApp.Core.Interfaces.Services;

namespace SohApp.Application.EventHandlers.OrderEventHandlers;

public class OrderCreatedEventHandler : IDomainEventHandler<OrderCreatedEvent>
{
    private readonly IEmailService _emailService;
    private readonly IUnitOfWork _unitOfWork;
    private readonly ILogger<OrderCreatedEventHandler> _logger;

    public OrderCreatedEventHandler(
        IEmailService emailService,
        IUnitOfWork unitOfWork,
        ILogger<OrderCreatedEventHandler> logger)
    {
        _emailService = emailService;
        _unitOfWork = unitOfWork;
        _logger = logger;
    }

    public async Task Handle(OrderCreatedEvent domainEvent, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Handling OrderCreatedEvent for order {OrderId}", domainEvent.OrderId);

            // Get user email
            var user = await _unitOfWork.Users.GetByIdAsync(domainEvent.UserId, cancellationToken);
            if (user != null)
            {
                // Get order details
                var order = await _unitOfWork.Orders.GetByIdAsync(domainEvent.OrderId, cancellationToken);
                if (order != null)
                {
                    // Send order confirmation email
                    await _emailService.SendOrderConfirmationEmailAsync(
                        user.Email,
                        order.OrderNumber,
                        domainEvent.TotalAmount,
                        cancellationToken);

                    _logger.LogInformation("Order confirmation email sent successfully for order {OrderId}", domainEvent.OrderId);
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error handling OrderCreatedEvent for order {OrderId}", domainEvent.OrderId);
            // Don't rethrow - we don't want email failures to break order creation
        }
    }
}
