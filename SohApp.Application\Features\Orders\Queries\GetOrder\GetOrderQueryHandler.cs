using AutoMapper;
using MediatR;
using Microsoft.Extensions.Logging;
using SohApp.Application.Common;
using SohApp.Core.Interfaces;

namespace SohApp.Application.Features.Orders.Queries.GetOrder;

public class GetOrderQueryHandler : IRequestHandler<GetOrderQuery, Result<OrderDetailDto>>
{
    private readonly IUnitOfWork _unitOfWork;
    private readonly IMapper _mapper;
    private readonly ILogger<GetOrderQueryHandler> _logger;

    public GetOrderQueryHandler(
        IUnitOfWork unitOfWork,
        IMapper mapper,
        ILogger<GetOrderQueryHandler> logger)
    {
        _unitOfWork = unitOfWork;
        _mapper = mapper;
        _logger = logger;
    }

    public async Task<Result<OrderDetailDto>> Handle(GetOrderQuery request, CancellationToken cancellationToken)
    {
        try
        {
            var order = await _unitOfWork.Orders.GetByIdAsync(request.Id, 
                o => o.User, 
                o => o.OrderItems);
            
            if (order == null)
            {
                return Result<OrderDetailDto>.Failure("Order not found");
            }

            var orderDetailDto = _mapper.Map<OrderDetailDto>(order);
            return Result<OrderDetailDto>.Success(orderDetailDto);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving order with ID: {OrderId}", request.Id);
            return Result<OrderDetailDto>.Failure("An error occurred while retrieving the order");
        }
    }
}
