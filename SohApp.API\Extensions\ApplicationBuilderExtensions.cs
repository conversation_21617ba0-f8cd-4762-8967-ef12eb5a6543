using SohApp.API.Middleware;

namespace SohApp.API.Extensions;

public static class ApplicationBuilderExtensions
{
    public static IApplicationBuilder UseApiMiddleware(this IApplicationBuilder app, IWebHostEnvironment env)
    {
        // Exception handling
        app.UseMiddleware<ExceptionHandlingMiddleware>();

        // Logging
        app.UseMiddleware<LoggingMiddleware>();

        // Development specific middleware
        if (env.IsDevelopment())
        {
            app.UseSwagger();
            app.UseSwaggerUI(c =>
            {
                c.SwaggerEndpoint("/swagger/v1/swagger.json", "SohApp API v1");
                c.RoutePrefix = string.Empty; // Set Swagger UI at the app's root
            });
        }

        // HTTPS redirection
        app.UseHttpsRedirection();

        // CORS
        app.UseCors("AllowAll");

        // Rate Limiting
        app.UseRateLimiter();

        // Authentication & Authorization
        app.UseAuthentication();
        app.UseAuthorization();

        return app;
    }
}
