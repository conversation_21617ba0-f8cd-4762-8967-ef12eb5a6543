using MediatR;
using Microsoft.Extensions.Logging;
using SohApp.Application.Common;
using SohApp.Application.DTOs.Auth;
using SohApp.Application.Interfaces;

namespace SohApp.Application.Features.Auth.Commands.Login;

public class LoginCommandHandler : IRequestHandler<LoginCommand, Result<LoginResponseDto>>
{
    private readonly IAuthenticationService _authenticationService;
    private readonly ILogger<LoginCommandHandler> _logger;

    public LoginCommandHandler(
        IAuthenticationService authenticationService,
        ILogger<LoginCommandHandler> logger)
    {
        _authenticationService = authenticationService;
        _logger = logger;
    }

    public async Task<Result<LoginResponseDto>> Handle(LoginCommand request, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Processing login request for email: {Email}", request.Email);

        var loginDto = new LoginDto
        {
            Email = request.Email,
            Password = request.Password
        };

        var result = await _authenticationService.LoginAsync(loginDto, request.IpAddress, cancellationToken);

        if (result.IsSuccess)
        {
            _logger.LogInformation("Login successful for email: {Email}", request.Email);
        }
        else
        {
            _logger.LogWarning("Login failed for email: {Email}. Errors: {Errors}", request.Email, string.Join(", ", result.Errors));
        }

        return result;
    }
}
