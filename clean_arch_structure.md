# Complete Clean Architecture Structure with Generic Repository, Unit of Work & Specifications

## Project Dependencies & Architecture Overview
```
API → Application → Core ← Infrastructure
API → Infrastructure (for DI registration only)
```

### Architecture Principles
- **Dependency Inversion**: Inner layers define interfaces, outer layers implement them
- **Single Responsibility**: Each layer has one reason to change
- **Open/Closed**: Open for extension, closed for modification
- **Interface Segregation**: Clients depend only on interfaces they use
- **Don't Repeat Yourself**: Reusable components across the application

### Layer Responsibilities
- **Core/Domain**: Business entities, rules, and interfaces (no dependencies)
- **Application**: Use cases, business logic orchestration, DTOs
- **Infrastructure**: Data access, external services, framework implementations
- **API**: Controllers, middleware, configuration, presentation logic

## 1. **Core/Domain Project** (No dependencies)

```
MyApp.Core/
├── Entities/
│   ├── Base/
│   │   ├── BaseEntity.cs
│   │   ├── IAuditableEntity.cs
│   │   ├── ISoftDeletable.cs
│   │   └── IHasTimestamps.cs
│   ├── User.cs
│   ├── Product.cs
│   ├── Order.cs
│   ├── OrderItem.cs
│   ├── Category.cs
│   └── AuditLog.cs
├── ValueObjects/
│   ├── Email.cs
│   ├── Money.cs
│   ├── Address.cs
│   ├── PhoneNumber.cs
│   ├── DateRange.cs
│   └── Percentage.cs
├── Interfaces/
│   ├── Repositories/
│   │   ├── IGenericRepository.cs
│   │   ├── IUserRepository.cs
│   │   ├── IProductRepository.cs
│   │   ├── IOrderRepository.cs
│   │   ├── ICategoryRepository.cs
│   │   └── IAuditLogRepository.cs
│   ├── Services/
│   │   ├── IEmailService.cs
│   │   ├── IFileStorageService.cs
│   │   ├── ICacheService.cs
│   │   ├── INotificationService.cs
│   │   └── ICurrentUserService.cs
│   ├── IUnitOfWork.cs
│   ├── ISpecification.cs
│   └── IDomainEventHandler.cs
├── Specifications/
│   ├── Base/
│   │   ├── BaseSpecification.cs
│   │   └── ISpecification.cs
│   ├── UserSpecifications/
│   │   ├── UserByEmailSpecification.cs
│   │   ├── ActiveUsersSpecification.cs
│   │   └── UsersByRoleSpecification.cs
│   ├── ProductSpecifications/
│   │   ├── ProductsByCategorySpecification.cs
│   │   ├── ProductsInStockSpecification.cs
│   │   └── ProductsByPriceRangeSpecification.cs
│   └── OrderSpecifications/
│       ├── OrdersByUserSpecification.cs
│       ├── OrdersByDateRangeSpecification.cs
│       └── PendingOrdersSpecification.cs
├── Enums/
│   ├── UserRole.cs
│   ├── OrderStatus.cs
│   └── ProductCategory.cs
├── Exceptions/
│   ├── DomainException.cs
│   ├── NotFoundException.cs
│   ├── ValidationException.cs
│   ├── BusinessRuleException.cs
│   └── ConcurrencyException.cs
├── Events/
│   ├── Base/
│   │   ├── IDomainEvent.cs
│   │   └── DomainEvent.cs
│   ├── UserEvents/
│   │   ├── UserCreatedEvent.cs
│   │   ├── UserUpdatedEvent.cs
│   │   └── UserDeletedEvent.cs
│   └── OrderEvents/
│       ├── OrderCreatedEvent.cs
│       ├── OrderStatusChangedEvent.cs
│       └── OrderCancelledEvent.cs
├── Constants/
│   ├── DomainConstants.cs
│   ├── ValidationConstants.cs
│   └── CacheKeys.cs
└── Extensions/
    ├── StringExtensions.cs
    ├── DateTimeExtensions.cs
    └── EnumExtensions.cs
```

## 2. **Infrastructure/Repository Project** (References: Core)

```
MyApp.Infrastructure/
├── Data/
│   ├── ApplicationDbContext.cs
│   ├── Configurations/
│   │   ├── UserConfiguration.cs
│   │   ├── ProductConfiguration.cs
│   │   └── OrderConfiguration.cs
│   └── Migrations/
├── Repositories/
│   ├── GenericRepository.cs
│   ├── UserRepository.cs
│   ├── ProductRepository.cs
│   ├── OrderRepository.cs
│   └── UnitOfWork.cs
├── Services/
│   ├── EmailService.cs
│   ├── FileStorageService.cs
│   ├── ExternalApiService.cs
│   ├── CacheService.cs
│   ├── NotificationService.cs
│   ├── CurrentUserService.cs
│   └── BackgroundJobService.cs
├── Specifications/
│   └── SpecificationEvaluator.cs
├── Extensions/
│   ├── QueryableExtensions.cs
│   ├── ModelBuilderExtensions.cs
│   └── ServiceCollectionExtensions.cs
├── Interceptors/
│   ├── AuditInterceptor.cs
│   ├── SoftDeleteInterceptor.cs
│   └── DomainEventInterceptor.cs
├── Caching/
│   ├── CacheService.cs
│   ├── DistributedCacheService.cs
│   └── CacheKeyGenerator.cs
├── BackgroundJobs/
│   ├── EmailJob.cs
│   ├── DataCleanupJob.cs
│   └── ReportGenerationJob.cs
└── DependencyInjection.cs
```

## 3. **Application/Service Project** (References: Core)

```
MyApp.Application/
├── DTOs/
│   ├── Common/
│   │   ├── PagedResult.cs
│   │   └── PaginationParameters.cs
│   ├── User/
│   │   ├── UserDto.cs
│   │   ├── CreateUserDto.cs
│   │   ├── UpdateUserDto.cs
│   │   └── UserFilterDto.cs
│   ├── Product/
│   │   ├── ProductDto.cs
│   │   ├── CreateProductDto.cs
│   │   └── ProductFilterDto.cs
│   └── Order/
│       ├── OrderDto.cs
│       ├── CreateOrderDto.cs
│       └── OrderFilterDto.cs
├── Services/
│   ├── UserService.cs
│   ├── ProductService.cs
│   └── OrderService.cs
├── Interfaces/
│   ├── IUserService.cs
│   ├── IProductService.cs
│   └── IOrderService.cs
├── Mappings/
│   └── MappingProfile.cs
├── Validators/
│   ├── CreateUserValidator.cs
│   ├── UpdateUserValidator.cs
│   └── CreateProductValidator.cs
├── Exceptions/
│   └── ApplicationException.cs
├── Common/
│   ├── Result.cs
│   ├── PagedList.cs
│   ├── ApiResponse.cs
│   └── OperationResult.cs
├── Behaviors/
│   ├── ValidationBehavior.cs
│   ├── LoggingBehavior.cs
│   ├── CachingBehavior.cs
│   └── PerformanceBehavior.cs
├── Features/
│   ├── Users/
│   │   ├── Commands/
│   │   │   ├── CreateUser/
│   │   │   │   ├── CreateUserCommand.cs
│   │   │   │   ├── CreateUserCommandHandler.cs
│   │   │   │   └── CreateUserCommandValidator.cs
│   │   │   ├── UpdateUser/
│   │   │   │   ├── UpdateUserCommand.cs
│   │   │   │   ├── UpdateUserCommandHandler.cs
│   │   │   │   └── UpdateUserCommandValidator.cs
│   │   │   └── DeleteUser/
│   │   │       ├── DeleteUserCommand.cs
│   │   │       └── DeleteUserCommandHandler.cs
│   │   └── Queries/
│   │       ├── GetUser/
│   │       │   ├── GetUserQuery.cs
│   │       │   └── GetUserQueryHandler.cs
│   │       ├── GetUsers/
│   │       │   ├── GetUsersQuery.cs
│   │       │   └── GetUsersQueryHandler.cs
│   │       └── GetUsersByRole/
│   │           ├── GetUsersByRoleQuery.cs
│   │           └── GetUsersByRoleQueryHandler.cs
│   ├── Products/
│   │   ├── Commands/
│   │   │   ├── CreateProduct/
│   │   │   ├── UpdateProduct/
│   │   │   └── DeleteProduct/
│   │   └── Queries/
│   │       ├── GetProduct/
│   │       ├── GetProducts/
│   │       └── GetProductsByCategory/
│   └── Orders/
│       ├── Commands/
│       │   ├── CreateOrder/
│       │   ├── UpdateOrderStatus/
│       │   └── CancelOrder/
│       └── Queries/
│           ├── GetOrder/
│           ├── GetOrders/
│           └── GetOrdersByUser/
├── EventHandlers/
│   ├── UserEventHandlers/
│   │   ├── UserCreatedEventHandler.cs
│   │   ├── UserUpdatedEventHandler.cs
│   │   └── UserDeletedEventHandler.cs
│   └── OrderEventHandlers/
│       ├── OrderCreatedEventHandler.cs
│       ├── OrderStatusChangedEventHandler.cs
│       └── OrderCancelledEventHandler.cs
└── DependencyInjection.cs
```

## 4. **API Project** (References: Application, Infrastructure)

```
MyApp.API/
├── Controllers/
│   ├── Base/
│   │   └── BaseController.cs
│   ├── UsersController.cs
│   ├── ProductsController.cs
│   └── OrdersController.cs
├── Middleware/
│   ├── ExceptionHandlingMiddleware.cs
│   ├── LoggingMiddleware.cs
│   └── ValidationMiddleware.cs
├── Filters/
│   ├── ValidationFilter.cs
│   └── CacheFilter.cs
├── Extensions/
│   ├── ServiceCollectionExtensions.cs
│   ├── ApplicationBuilderExtensions.cs
│   └── ControllerExtensions.cs
├── Models/
│   ├── ApiResponse.cs
│   ├── ErrorResponse.cs
│   ├── PaginationRequest.cs
│   └── ValidationErrorResponse.cs
├── Attributes/
│   ├── CacheAttribute.cs
│   ├── RateLimitAttribute.cs
│   └── AuthorizeRoleAttribute.cs
├── Configurations/
│   ├── SwaggerConfiguration.cs
│   ├── CorsConfiguration.cs
│   ├── JwtConfiguration.cs
│   └── DatabaseConfiguration.cs
├── Program.cs
├── appsettings.json
├── appsettings.Development.json
├── appsettings.Production.json
└── appsettings.Staging.json
```

## Key Implementation Examples:

### **Core Layer Implementations**

#### **Base Entity & Interfaces (Core)**
```csharp
// Base interfaces
public interface IHasTimestamps
{
    DateTime CreatedAt { get; set; }
    DateTime? UpdatedAt { get; set; }
}

public interface ISoftDeletable
{
    bool IsDeleted { get; set; }
    DateTime? DeletedAt { get; set; }
}

public interface IAuditableEntity : IHasTimestamps
{
    string CreatedBy { get; set; }
    string? UpdatedBy { get; set; }
}

// Base entity
public abstract class BaseEntity : IAuditableEntity, ISoftDeletable
{
    public int Id { get; set; }
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
    public DateTime? UpdatedAt { get; set; }
    public string CreatedBy { get; set; } = string.Empty;
    public string? UpdatedBy { get; set; }
    public bool IsDeleted { get; set; }
    public DateTime? DeletedAt { get; set; }

    private readonly List<IDomainEvent> _domainEvents = new();
    public IReadOnlyCollection<IDomainEvent> DomainEvents => _domainEvents.AsReadOnly();

    public void AddDomainEvent(IDomainEvent domainEvent)
    {
        _domainEvents.Add(domainEvent);
    }

    public void RemoveDomainEvent(IDomainEvent domainEvent)
    {
        _domainEvents.Remove(domainEvent);
    }

    public void ClearDomainEvents()
    {
        _domainEvents.Clear();
    }
}
```

#### **Domain Events (Core)**
```csharp
public interface IDomainEvent
{
    DateTime OccurredOn { get; }
    Guid EventId { get; }
}

public abstract class DomainEvent : IDomainEvent
{
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
    public Guid EventId { get; } = Guid.NewGuid();
}

// Example domain events
public class UserCreatedEvent : DomainEvent
{
    public int UserId { get; }
    public string Email { get; }
    public string FullName { get; }

    public UserCreatedEvent(int userId, string email, string fullName)
    {
        UserId = userId;
        Email = email;
        FullName = fullName;
    }
}

public class OrderCreatedEvent : DomainEvent
{
    public int OrderId { get; }
    public int UserId { get; }
    public decimal TotalAmount { get; }

    public OrderCreatedEvent(int orderId, int userId, decimal totalAmount)
    {
        OrderId = orderId;
        UserId = userId;
        TotalAmount = totalAmount;
    }
}
```

#### **Value Objects (Core)**
```csharp
public class Email : IEquatable<Email>
{
    public string Value { get; }

    public Email(string value)
    {
        if (string.IsNullOrWhiteSpace(value))
            throw new ArgumentException("Email cannot be empty", nameof(value));

        if (!IsValidEmail(value))
            throw new ArgumentException("Invalid email format", nameof(value));

        Value = value.ToLowerInvariant();
    }

    private static bool IsValidEmail(string email)
    {
        try
        {
            var addr = new System.Net.Mail.MailAddress(email);
            return addr.Address == email;
        }
        catch
        {
            return false;
        }
    }

    public bool Equals(Email? other) => other is not null && Value == other.Value;
    public override bool Equals(object? obj) => Equals(obj as Email);
    public override int GetHashCode() => Value.GetHashCode();
    public override string ToString() => Value;

    public static implicit operator string(Email email) => email.Value;
    public static explicit operator Email(string email) => new(email);
}

public class Money : IEquatable<Money>
{
    public decimal Amount { get; }
    public string Currency { get; }

    public Money(decimal amount, string currency = "USD")
    {
        if (amount < 0)
            throw new ArgumentException("Amount cannot be negative", nameof(amount));

        if (string.IsNullOrWhiteSpace(currency))
            throw new ArgumentException("Currency cannot be empty", nameof(currency));

        Amount = Math.Round(amount, 2);
        Currency = currency.ToUpperInvariant();
    }

    public Money Add(Money other)
    {
        if (Currency != other.Currency)
            throw new InvalidOperationException("Cannot add money with different currencies");

        return new Money(Amount + other.Amount, Currency);
    }

    public Money Subtract(Money other)
    {
        if (Currency != other.Currency)
            throw new InvalidOperationException("Cannot subtract money with different currencies");

        return new Money(Amount - other.Amount, Currency);
    }

    public bool Equals(Money? other) =>
        other is not null && Amount == other.Amount && Currency == other.Currency;

    public override bool Equals(object? obj) => Equals(obj as Money);
    public override int GetHashCode() => HashCode.Combine(Amount, Currency);
    public override string ToString() => $"{Amount:C} {Currency}";

    public static Money operator +(Money left, Money right) => left.Add(right);
    public static Money operator -(Money left, Money right) => left.Subtract(right);
}
```

#### **Entity Examples (Core)**
```csharp
public class User : BaseEntity
{
    public string FirstName { get; set; } = string.Empty;
    public string LastName { get; set; } = string.Empty;
    public Email Email { get; set; } = null!;
    public string PasswordHash { get; set; } = string.Empty;
    public UserRole Role { get; set; }
    public bool IsActive { get; set; } = true;
    public DateTime? LastLoginAt { get; set; }

    // Navigation properties
    public virtual ICollection<Order> Orders { get; set; } = new List<Order>();

    public string FullName => $"{FirstName} {LastName}";

    public void Activate()
    {
        IsActive = true;
        AddDomainEvent(new UserActivatedEvent(Id, Email.Value));
    }

    public void Deactivate()
    {
        IsActive = false;
        AddDomainEvent(new UserDeactivatedEvent(Id, Email.Value));
    }

    public void UpdateLastLogin()
    {
        LastLoginAt = DateTime.UtcNow;
    }
}

public class Product : BaseEntity
{
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string Sku { get; set; } = string.Empty;
    public Money Price { get; set; } = null!;
    public int StockQuantity { get; set; }
    public int CategoryId { get; set; }
    public bool IsActive { get; set; } = true;

    // Navigation properties
    public virtual Category Category { get; set; } = null!;
    public virtual ICollection<OrderItem> OrderItems { get; set; } = new List<OrderItem>();

    public void UpdateStock(int quantity)
    {
        if (StockQuantity + quantity < 0)
            throw new BusinessRuleException("Insufficient stock");

        StockQuantity += quantity;
        AddDomainEvent(new ProductStockUpdatedEvent(Id, StockQuantity));
    }

    public void UpdatePrice(Money newPrice)
    {
        var oldPrice = Price;
        Price = newPrice;
        AddDomainEvent(new ProductPriceChangedEvent(Id, oldPrice, newPrice));
    }
}

public class Order : BaseEntity
{
    public int UserId { get; set; }
    public string OrderNumber { get; set; } = string.Empty;
    public OrderStatus Status { get; set; } = OrderStatus.Pending;
    public Money TotalAmount { get; set; } = null!;
    public DateTime? ShippedAt { get; set; }
    public DateTime? DeliveredAt { get; set; }

    // Navigation properties
    public virtual User User { get; set; } = null!;
    public virtual ICollection<OrderItem> OrderItems { get; set; } = new List<OrderItem>();

    public void AddItem(Product product, int quantity)
    {
        if (Status != OrderStatus.Pending)
            throw new BusinessRuleException("Cannot modify confirmed order");

        var existingItem = OrderItems.FirstOrDefault(x => x.ProductId == product.Id);
        if (existingItem != null)
        {
            existingItem.UpdateQuantity(existingItem.Quantity + quantity);
        }
        else
        {
            var orderItem = new OrderItem
            {
                ProductId = product.Id,
                Product = product,
                Quantity = quantity,
                UnitPrice = product.Price,
                TotalPrice = new Money(product.Price.Amount * quantity, product.Price.Currency)
            };
            OrderItems.Add(orderItem);
        }

        RecalculateTotal();
    }

    public void Confirm()
    {
        if (Status != OrderStatus.Pending)
            throw new BusinessRuleException("Order is already confirmed");

        Status = OrderStatus.Confirmed;
        AddDomainEvent(new OrderConfirmedEvent(Id, UserId, TotalAmount.Amount));
    }

    private void RecalculateTotal()
    {
        var total = OrderItems.Sum(x => x.TotalPrice.Amount);
        TotalAmount = new Money(total, "USD");
    }
}
```

### **Repository Interfaces (Core)**

#### **Generic Repository Interface**
```csharp
public interface IGenericRepository<T> where T : BaseEntity
{
    // Query methods
    Task<T?> GetByIdAsync(int id, CancellationToken cancellationToken = default);
    Task<T?> GetByIdAsync(int id, params Expression<Func<T, object>>[] includes);
    Task<IReadOnlyList<T>> GetAllAsync(CancellationToken cancellationToken = default);
    Task<IReadOnlyList<T>> GetAsync(ISpecification<T> spec, CancellationToken cancellationToken = default);
    Task<T?> GetEntityWithSpec(ISpecification<T> spec, CancellationToken cancellationToken = default);
    Task<int> CountAsync(ISpecification<T> spec, CancellationToken cancellationToken = default);
    Task<bool> ExistsAsync(int id, CancellationToken cancellationToken = default);
    Task<bool> ExistsAsync(ISpecification<T> spec, CancellationToken cancellationToken = default);

    // Command methods
    Task<T> AddAsync(T entity, CancellationToken cancellationToken = default);
    Task<IEnumerable<T>> AddRangeAsync(IEnumerable<T> entities, CancellationToken cancellationToken = default);
    void Update(T entity);
    void UpdateRange(IEnumerable<T> entities);
    void Delete(T entity);
    void DeleteRange(IEnumerable<T> entities);
    Task DeleteByIdAsync(int id, CancellationToken cancellationToken = default);

    // Pagination
    Task<PagedResult<T>> GetPagedAsync(ISpecification<T> spec, int pageNumber, int pageSize, CancellationToken cancellationToken = default);
}
```

#### **Specific Repository Interfaces**
```csharp
public interface IUserRepository : IGenericRepository<User>
{
    Task<User?> GetByEmailAsync(string email, CancellationToken cancellationToken = default);
    Task<IReadOnlyList<User>> GetActiveUsersAsync(CancellationToken cancellationToken = default);
    Task<IReadOnlyList<User>> GetUsersByRoleAsync(UserRole role, CancellationToken cancellationToken = default);
    Task<bool> IsEmailUniqueAsync(string email, int? excludeUserId = null, CancellationToken cancellationToken = default);
}

public interface IProductRepository : IGenericRepository<Product>
{
    Task<IReadOnlyList<Product>> GetByCategoryAsync(int categoryId, CancellationToken cancellationToken = default);
    Task<IReadOnlyList<Product>> GetInStockAsync(CancellationToken cancellationToken = default);
    Task<IReadOnlyList<Product>> GetByPriceRangeAsync(decimal minPrice, decimal maxPrice, CancellationToken cancellationToken = default);
    Task<Product?> GetBySkuAsync(string sku, CancellationToken cancellationToken = default);
}

public interface IOrderRepository : IGenericRepository<Order>
{
    Task<IReadOnlyList<Order>> GetByUserIdAsync(int userId, CancellationToken cancellationToken = default);
    Task<IReadOnlyList<Order>> GetByDateRangeAsync(DateTime startDate, DateTime endDate, CancellationToken cancellationToken = default);
    Task<IReadOnlyList<Order>> GetPendingOrdersAsync(CancellationToken cancellationToken = default);
    Task<string> GenerateOrderNumberAsync(CancellationToken cancellationToken = default);
}
```

### **Unit of Work Interface (Core)**
```csharp
public interface IUnitOfWork : IDisposable
{
    // Repository properties
    IUserRepository Users { get; }
    IProductRepository Products { get; }
    IOrderRepository Orders { get; }
    ICategoryRepository Categories { get; }
    IAuditLogRepository AuditLogs { get; }

    // Transaction methods
    Task<int> SaveChangesAsync(CancellationToken cancellationToken = default);
    Task<int> SaveChangesAsync(string userId, CancellationToken cancellationToken = default);
    Task BeginTransactionAsync(CancellationToken cancellationToken = default);
    Task CommitTransactionAsync(CancellationToken cancellationToken = default);
    Task RollbackTransactionAsync(CancellationToken cancellationToken = default);

    // Generic repository access
    IGenericRepository<T> Repository<T>() where T : BaseEntity;

    // Domain events
    Task PublishDomainEventsAsync(CancellationToken cancellationToken = default);
}
```

### **Specification Pattern (Core)**

#### **Specification Interfaces**
```csharp
public interface ISpecification<T>
{
    Expression<Func<T, bool>>? Criteria { get; }
    List<Expression<Func<T, object>>> Includes { get; }
    List<string> IncludeStrings { get; }
    Expression<Func<T, object>>? OrderBy { get; }
    Expression<Func<T, object>>? OrderByDescending { get; }
    List<(Expression<Func<T, object>> KeySelector, bool Descending)> ThenByExpressions { get; }
    int Take { get; }
    int Skip { get; }
    bool IsPagingEnabled { get; }
    bool IsSplitQuery { get; }
    bool AsNoTracking { get; }
}

public interface ISpecification<T, TResult> : ISpecification<T>
{
    Expression<Func<T, TResult>>? Selector { get; }
}
```

#### **Base Specification Implementation**
```csharp
public abstract class BaseSpecification<T> : ISpecification<T>
{
    public Expression<Func<T, bool>>? Criteria { get; private set; }
    public List<Expression<Func<T, object>>> Includes { get; } = new();
    public List<string> IncludeStrings { get; } = new();
    public Expression<Func<T, object>>? OrderBy { get; private set; }
    public Expression<Func<T, object>>? OrderByDescending { get; private set; }
    public List<(Expression<Func<T, object>> KeySelector, bool Descending)> ThenByExpressions { get; } = new();
    public int Take { get; private set; }
    public int Skip { get; private set; }
    public bool IsPagingEnabled { get; private set; }
    public bool IsSplitQuery { get; private set; }
    public bool AsNoTracking { get; private set; }

    protected BaseSpecification() { }

    protected BaseSpecification(Expression<Func<T, bool>> criteria)
    {
        Criteria = criteria;
    }

    protected virtual void AddInclude(Expression<Func<T, object>> includeExpression)
    {
        Includes.Add(includeExpression);
    }

    protected virtual void AddInclude(string includeString)
    {
        IncludeStrings.Add(includeString);
    }

    protected virtual void ApplyPaging(int skip, int take)
    {
        Skip = skip;
        Take = take;
        IsPagingEnabled = true;
    }

    protected virtual void ApplyOrderBy(Expression<Func<T, object>> orderByExpression)
    {
        OrderBy = orderByExpression;
    }

    protected virtual void ApplyOrderByDescending(Expression<Func<T, object>> orderByDescExpression)
    {
        OrderByDescending = orderByDescExpression;
    }

    protected virtual void ApplyThenBy(Expression<Func<T, object>> thenByExpression)
    {
        ThenByExpressions.Add((thenByExpression, false));
    }

    protected virtual void ApplyThenByDescending(Expression<Func<T, object>> thenByDescExpression)
    {
        ThenByExpressions.Add((thenByDescExpression, true));
    }

    protected virtual void EnableSplitQuery()
    {
        IsSplitQuery = true;
    }

    protected virtual void EnableNoTracking()
    {
        AsNoTracking = true;
    }

    protected virtual void SetCriteria(Expression<Func<T, bool>> criteria)
    {
        Criteria = criteria;
    }
}

public abstract class BaseSpecification<T, TResult> : BaseSpecification<T>, ISpecification<T, TResult>
{
    public Expression<Func<T, TResult>>? Selector { get; private set; }

    protected BaseSpecification(Expression<Func<T, TResult>> selector) : base()
    {
        Selector = selector;
    }

    protected BaseSpecification(Expression<Func<T, bool>> criteria, Expression<Func<T, TResult>> selector) : base(criteria)
    {
        Selector = selector;
    }
}
```

#### **Specification Examples**
```csharp
// User specifications
public class UserByEmailSpecification : BaseSpecification<User>
{
    public UserByEmailSpecification(string email) : base(u => u.Email.Value == email.ToLowerInvariant())
    {
        AddInclude(u => u.Orders);
    }
}

public class ActiveUsersSpecification : BaseSpecification<User>
{
    public ActiveUsersSpecification() : base(u => u.IsActive && !u.IsDeleted)
    {
        ApplyOrderBy(u => u.LastName);
        ApplyThenBy(u => u.FirstName);
        EnableNoTracking();
    }
}

public class UsersByRoleSpecification : BaseSpecification<User>
{
    public UsersByRoleSpecification(UserRole role) : base(u => u.Role == role && !u.IsDeleted)
    {
        ApplyOrderBy(u => u.CreatedAt);
    }
}

// Product specifications
public class ProductsByCategorySpecification : BaseSpecification<Product>
{
    public ProductsByCategorySpecification(int categoryId) : base(p => p.CategoryId == categoryId && p.IsActive)
    {
        AddInclude(p => p.Category);
        ApplyOrderBy(p => p.Name);
    }
}

public class ProductsInStockSpecification : BaseSpecification<Product>
{
    public ProductsInStockSpecification() : base(p => p.StockQuantity > 0 && p.IsActive)
    {
        ApplyOrderByDescending(p => p.StockQuantity);
    }
}

public class ProductsByPriceRangeSpecification : BaseSpecification<Product>
{
    public ProductsByPriceRangeSpecification(decimal minPrice, decimal maxPrice)
        : base(p => p.Price.Amount >= minPrice && p.Price.Amount <= maxPrice && p.IsActive)
    {
        ApplyOrderBy(p => p.Price.Amount);
    }
}
```

### **Infrastructure Layer Implementations**

#### **Specification Evaluator (Infrastructure)**
```csharp
public static class SpecificationEvaluator
{
    public static IQueryable<T> GetQuery<T>(IQueryable<T> inputQuery, ISpecification<T> specification) where T : BaseEntity
    {
        var query = inputQuery;

        // Apply no tracking if specified
        if (specification.AsNoTracking)
        {
            query = query.AsNoTracking();
        }

        // Apply criteria
        if (specification.Criteria != null)
        {
            query = query.Where(specification.Criteria);
        }

        // Apply includes
        query = specification.Includes.Aggregate(query, (current, include) => current.Include(include));
        query = specification.IncludeStrings.Aggregate(query, (current, include) => current.Include(include));

        // Apply split query if specified
        if (specification.IsSplitQuery)
        {
            query = query.AsSplitQuery();
        }

        // Apply ordering
        if (specification.OrderBy != null)
        {
            query = query.OrderBy(specification.OrderBy);
        }
        else if (specification.OrderByDescending != null)
        {
            query = query.OrderByDescending(specification.OrderByDescending);
        }

        // Apply ThenBy expressions
        var orderedQuery = query as IOrderedQueryable<T>;
        if (orderedQuery != null)
        {
            foreach (var (keySelector, descending) in specification.ThenByExpressions)
            {
                orderedQuery = descending
                    ? orderedQuery.ThenByDescending(keySelector)
                    : orderedQuery.ThenBy(keySelector);
            }
            query = orderedQuery;
        }

        // Apply paging (must be last)
        if (specification.IsPagingEnabled)
        {
            query = query.Skip(specification.Skip).Take(specification.Take);
        }

        return query;
    }

    public static IQueryable<TResult> GetQuery<T, TResult>(IQueryable<T> inputQuery, ISpecification<T, TResult> specification) where T : BaseEntity
    {
        var query = GetQuery(inputQuery, (ISpecification<T>)specification);

        if (specification.Selector != null)
        {
            return query.Select(specification.Selector);
        }

        throw new InvalidOperationException("Selector must be specified for projection specifications");
    }
}
```

#### **Database Context (Infrastructure)**
```csharp
public class ApplicationDbContext : DbContext
{
    private readonly ICurrentUserService _currentUserService;
    private readonly IDomainEventDispatcher _domainEventDispatcher;

    public ApplicationDbContext(
        DbContextOptions<ApplicationDbContext> options,
        ICurrentUserService currentUserService,
        IDomainEventDispatcher domainEventDispatcher) : base(options)
    {
        _currentUserService = currentUserService;
        _domainEventDispatcher = domainEventDispatcher;
    }

    public DbSet<User> Users => Set<User>();
    public DbSet<Product> Products => Set<Product>();
    public DbSet<Order> Orders => Set<Order>();
    public DbSet<OrderItem> OrderItems => Set<OrderItem>();
    public DbSet<Category> Categories => Set<Category>();
    public DbSet<AuditLog> AuditLogs => Set<AuditLog>();

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        base.OnModelCreating(modelBuilder);

        // Apply all configurations from assembly
        modelBuilder.ApplyConfigurationsFromAssembly(typeof(ApplicationDbContext).Assembly);

        // Apply global query filters
        foreach (var entityType in modelBuilder.Model.GetEntityTypes())
        {
            if (typeof(ISoftDeletable).IsAssignableFrom(entityType.ClrType))
            {
                var method = typeof(ApplicationDbContext)
                    .GetMethod(nameof(SetSoftDeleteFilter), BindingFlags.NonPublic | BindingFlags.Static)!
                    .MakeGenericMethod(entityType.ClrType);
                method.Invoke(null, new object[] { modelBuilder });
            }
        }
    }

    private static void SetSoftDeleteFilter<T>(ModelBuilder modelBuilder) where T : class, ISoftDeletable
    {
        modelBuilder.Entity<T>().HasQueryFilter(e => !e.IsDeleted);
    }

    public override async Task<int> SaveChangesAsync(CancellationToken cancellationToken = default)
    {
        // Update audit fields
        UpdateAuditFields();

        // Dispatch domain events before saving
        await DispatchDomainEventsAsync(cancellationToken);

        var result = await base.SaveChangesAsync(cancellationToken);

        return result;
    }

    private void UpdateAuditFields()
    {
        var currentUser = _currentUserService.UserId ?? "System";
        var now = DateTime.UtcNow;

        foreach (var entry in ChangeTracker.Entries<IAuditableEntity>())
        {
            switch (entry.State)
            {
                case EntityState.Added:
                    entry.Entity.CreatedBy = currentUser;
                    entry.Entity.CreatedAt = now;
                    break;
                case EntityState.Modified:
                    entry.Entity.UpdatedBy = currentUser;
                    entry.Entity.UpdatedAt = now;
                    break;
                case EntityState.Deleted:
                    if (entry.Entity is ISoftDeletable softDeletable)
                    {
                        entry.State = EntityState.Modified;
                        softDeletable.IsDeleted = true;
                        softDeletable.DeletedAt = now;
                        entry.Entity.UpdatedBy = currentUser;
                        entry.Entity.UpdatedAt = now;
                    }
                    break;
            }
        }
    }

    private async Task DispatchDomainEventsAsync(CancellationToken cancellationToken)
    {
        var domainEntities = ChangeTracker.Entries<BaseEntity>()
            .Where(x => x.Entity.DomainEvents.Any())
            .ToList();

        var domainEvents = domainEntities
            .SelectMany(x => x.Entity.DomainEvents)
            .ToList();

        domainEntities.ForEach(entity => entity.Entity.ClearDomainEvents());

        foreach (var domainEvent in domainEvents)
        {
            await _domainEventDispatcher.DispatchAsync(domainEvent, cancellationToken);
        }
    }
}
```

#### **Entity Configurations (Infrastructure)**
```csharp
public class UserConfiguration : IEntityTypeConfiguration<User>
{
    public void Configure(EntityTypeBuilder<User> builder)
    {
        builder.ToTable("Users");

        builder.HasKey(u => u.Id);

        builder.Property(u => u.FirstName)
            .IsRequired()
            .HasMaxLength(100);

        builder.Property(u => u.LastName)
            .IsRequired()
            .HasMaxLength(100);

        builder.OwnsOne(u => u.Email, email =>
        {
            email.Property(e => e.Value)
                .HasColumnName("Email")
                .IsRequired()
                .HasMaxLength(255);
        });

        builder.HasIndex(u => u.Email.Value)
            .IsUnique()
            .HasDatabaseName("IX_Users_Email");

        builder.Property(u => u.PasswordHash)
            .IsRequired()
            .HasMaxLength(500);

        builder.Property(u => u.Role)
            .IsRequired()
            .HasConversion<string>();

        builder.Property(u => u.IsActive)
            .IsRequired()
            .HasDefaultValue(true);

        builder.Property(u => u.CreatedAt)
            .IsRequired();

        builder.Property(u => u.CreatedBy)
            .IsRequired()
            .HasMaxLength(100);

        // Navigation properties
        builder.HasMany(u => u.Orders)
            .WithOne(o => o.User)
            .HasForeignKey(o => o.UserId)
            .OnDelete(DeleteBehavior.Restrict);
    }
}

public class ProductConfiguration : IEntityTypeConfiguration<Product>
{
    public void Configure(EntityTypeBuilder<Product> builder)
    {
        builder.ToTable("Products");

        builder.HasKey(p => p.Id);

        builder.Property(p => p.Name)
            .IsRequired()
            .HasMaxLength(200);

        builder.Property(p => p.Description)
            .HasMaxLength(1000);

        builder.Property(p => p.Sku)
            .IsRequired()
            .HasMaxLength(50);

        builder.HasIndex(p => p.Sku)
            .IsUnique()
            .HasDatabaseName("IX_Products_Sku");

        builder.OwnsOne(p => p.Price, price =>
        {
            price.Property(m => m.Amount)
                .HasColumnName("Price")
                .HasColumnType("decimal(18,2)")
                .IsRequired();

            price.Property(m => m.Currency)
                .HasColumnName("Currency")
                .HasMaxLength(3)
                .IsRequired()
                .HasDefaultValue("USD");
        });

        builder.Property(p => p.StockQuantity)
            .IsRequired()
            .HasDefaultValue(0);

        builder.Property(p => p.IsActive)
            .IsRequired()
            .HasDefaultValue(true);

        // Navigation properties
        builder.HasOne(p => p.Category)
            .WithMany(c => c.Products)
            .HasForeignKey(p => p.CategoryId)
            .OnDelete(DeleteBehavior.Restrict);
    }
}
```

#### **Generic Repository Implementation (Infrastructure)**
```csharp
public class GenericRepository<T> : IGenericRepository<T> where T : BaseEntity
{
    protected readonly ApplicationDbContext _context;
    protected readonly DbSet<T> _dbSet;

    public GenericRepository(ApplicationDbContext context)
    {
        _context = context;
        _dbSet = context.Set<T>();
    }

    public virtual async Task<T?> GetByIdAsync(int id, CancellationToken cancellationToken = default)
    {
        return await _dbSet.FindAsync(new object[] { id }, cancellationToken);
    }

    public virtual async Task<T?> GetByIdAsync(int id, params Expression<Func<T, object>>[] includes)
    {
        IQueryable<T> query = _dbSet;

        foreach (var include in includes)
        {
            query = query.Include(include);
        }

        return await query.FirstOrDefaultAsync(x => x.Id == id);
    }

    public virtual async Task<IReadOnlyList<T>> GetAllAsync(CancellationToken cancellationToken = default)
    {
        return await _dbSet.ToListAsync(cancellationToken);
    }

    public virtual async Task<IReadOnlyList<T>> GetAsync(ISpecification<T> spec, CancellationToken cancellationToken = default)
    {
        return await ApplySpecification(spec).ToListAsync(cancellationToken);
    }

    public virtual async Task<T?> GetEntityWithSpec(ISpecification<T> spec, CancellationToken cancellationToken = default)
    {
        return await ApplySpecification(spec).FirstOrDefaultAsync(cancellationToken);
    }

    public virtual async Task<int> CountAsync(ISpecification<T> spec, CancellationToken cancellationToken = default)
    {
        return await ApplySpecification(spec).CountAsync(cancellationToken);
    }

    public virtual async Task<bool> ExistsAsync(int id, CancellationToken cancellationToken = default)
    {
        return await _dbSet.AnyAsync(x => x.Id == id, cancellationToken);
    }

    public virtual async Task<bool> ExistsAsync(ISpecification<T> spec, CancellationToken cancellationToken = default)
    {
        return await ApplySpecification(spec).AnyAsync(cancellationToken);
    }

    public virtual async Task<T> AddAsync(T entity, CancellationToken cancellationToken = default)
    {
        await _dbSet.AddAsync(entity, cancellationToken);
        return entity;
    }

    public virtual async Task<IEnumerable<T>> AddRangeAsync(IEnumerable<T> entities, CancellationToken cancellationToken = default)
    {
        await _dbSet.AddRangeAsync(entities, cancellationToken);
        return entities;
    }

    public virtual void Update(T entity)
    {
        _dbSet.Attach(entity);
        _context.Entry(entity).State = EntityState.Modified;
    }

    public virtual void UpdateRange(IEnumerable<T> entities)
    {
        _dbSet.UpdateRange(entities);
    }

    public virtual void Delete(T entity)
    {
        if (_context.Entry(entity).State == EntityState.Detached)
        {
            _dbSet.Attach(entity);
        }
        _dbSet.Remove(entity);
    }

    public virtual void DeleteRange(IEnumerable<T> entities)
    {
        _dbSet.RemoveRange(entities);
    }

    public virtual async Task DeleteByIdAsync(int id, CancellationToken cancellationToken = default)
    {
        var entity = await GetByIdAsync(id, cancellationToken);
        if (entity != null)
        {
            Delete(entity);
        }
    }

    public virtual async Task<PagedResult<T>> GetPagedAsync(ISpecification<T> spec, int pageNumber, int pageSize, CancellationToken cancellationToken = default)
    {
        var totalCount = await CountAsync(spec, cancellationToken);

        // Create a new specification with paging
        var pagedSpec = new PagedSpecification<T>(spec, pageNumber, pageSize);
        var items = await GetAsync(pagedSpec, cancellationToken);

        return new PagedResult<T>
        {
            Data = items,
            TotalCount = totalCount,
            Page = pageNumber,
            PageSize = pageSize,
            TotalPages = (int)Math.Ceiling(totalCount / (double)pageSize)
        };
    }

    protected virtual IQueryable<T> ApplySpecification(ISpecification<T> spec)
    {
        return SpecificationEvaluator.GetQuery(_dbSet.AsQueryable(), spec);
    }
}

// Helper specification for pagination
internal class PagedSpecification<T> : BaseSpecification<T> where T : BaseEntity
{
    public PagedSpecification(ISpecification<T> baseSpec, int pageNumber, int pageSize)
    {
        if (baseSpec.Criteria != null)
            SetCriteria(baseSpec.Criteria);

        foreach (var include in baseSpec.Includes)
            AddInclude(include);

        foreach (var includeString in baseSpec.IncludeStrings)
            AddInclude(includeString);

        if (baseSpec.OrderBy != null)
            ApplyOrderBy(baseSpec.OrderBy);
        else if (baseSpec.OrderByDescending != null)
            ApplyOrderByDescending(baseSpec.OrderByDescending);

        foreach (var (keySelector, descending) in baseSpec.ThenByExpressions)
        {
            if (descending)
                ApplyThenByDescending(keySelector);
            else
                ApplyThenBy(keySelector);
        }

        if (baseSpec.IsSplitQuery)
            EnableSplitQuery();

        if (baseSpec.AsNoTracking)
            EnableNoTracking();

        ApplyPaging((pageNumber - 1) * pageSize, pageSize);
    }
}
```

### **Application Layer Implementations**

#### **CQRS Pattern with MediatR**

##### **Command Example - Create User**
```csharp
// Command
public class CreateUserCommand : IRequest<Result<int>>
{
    public string FirstName { get; set; } = string.Empty;
    public string LastName { get; set; } = string.Empty;
    public string Email { get; set; } = string.Empty;
    public string Password { get; set; } = string.Empty;
    public UserRole Role { get; set; }
}

// Command Handler
public class CreateUserCommandHandler : IRequestHandler<CreateUserCommand, Result<int>>
{
    private readonly IUnitOfWork _unitOfWork;
    private readonly IPasswordHasher _passwordHasher;
    private readonly IValidator<CreateUserCommand> _validator;
    private readonly ILogger<CreateUserCommandHandler> _logger;

    public CreateUserCommandHandler(
        IUnitOfWork unitOfWork,
        IPasswordHasher passwordHasher,
        IValidator<CreateUserCommand> validator,
        ILogger<CreateUserCommandHandler> logger)
    {
        _unitOfWork = unitOfWork;
        _passwordHasher = passwordHasher;
        _validator = validator;
        _logger = logger;
    }

    public async Task<Result<int>> Handle(CreateUserCommand request, CancellationToken cancellationToken)
    {
        try
        {
            // Validation
            var validationResult = await _validator.ValidateAsync(request, cancellationToken);
            if (!validationResult.IsValid)
            {
                return Result<int>.Failure(validationResult.Errors.Select(e => e.ErrorMessage));
            }

            // Check if email is unique
            var emailExists = await _unitOfWork.Users.IsEmailUniqueAsync(request.Email, cancellationToken: cancellationToken);
            if (!emailExists)
            {
                return Result<int>.Failure("Email already exists");
            }

            // Create user entity
            var user = new User
            {
                FirstName = request.FirstName,
                LastName = request.LastName,
                Email = new Email(request.Email),
                PasswordHash = _passwordHasher.HashPassword(request.Password),
                Role = request.Role,
                IsActive = true
            };

            // Add domain event
            user.AddDomainEvent(new UserCreatedEvent(user.Id, user.Email.Value, user.FullName));

            // Save to database
            await _unitOfWork.Users.AddAsync(user, cancellationToken);
            await _unitOfWork.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("User created successfully with ID: {UserId}", user.Id);

            return Result<int>.Success(user.Id);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating user with email: {Email}", request.Email);
            return Result<int>.Failure("An error occurred while creating the user");
        }
    }
}

// Command Validator
public class CreateUserCommandValidator : AbstractValidator<CreateUserCommand>
{
    public CreateUserCommandValidator()
    {
        RuleFor(x => x.FirstName)
            .NotEmpty().WithMessage("First name is required")
            .MaximumLength(100).WithMessage("First name must not exceed 100 characters");

        RuleFor(x => x.LastName)
            .NotEmpty().WithMessage("Last name is required")
            .MaximumLength(100).WithMessage("Last name must not exceed 100 characters");

        RuleFor(x => x.Email)
            .NotEmpty().WithMessage("Email is required")
            .EmailAddress().WithMessage("Invalid email format")
            .MaximumLength(255).WithMessage("Email must not exceed 255 characters");

        RuleFor(x => x.Password)
            .NotEmpty().WithMessage("Password is required")
            .MinimumLength(8).WithMessage("Password must be at least 8 characters")
            .Matches(@"^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]")
            .WithMessage("Password must contain at least one uppercase letter, one lowercase letter, one digit, and one special character");

        RuleFor(x => x.Role)
            .IsInEnum().WithMessage("Invalid user role");
    }
}
```

##### **Query Example - Get Users**
```csharp
// Query
public class GetUsersQuery : IRequest<Result<PagedResult<UserDto>>>
{
    public int Page { get; set; } = 1;
    public int PageSize { get; set; } = 10;
    public UserRole? Role { get; set; }
    public bool? IsActive { get; set; }
    public string? SearchTerm { get; set; }
    public string? SortBy { get; set; }
    public bool SortDescending { get; set; }
}

// Query Handler
public class GetUsersQueryHandler : IRequestHandler<GetUsersQuery, Result<PagedResult<UserDto>>>
{
    private readonly IUnitOfWork _unitOfWork;
    private readonly IMapper _mapper;
    private readonly ILogger<GetUsersQueryHandler> _logger;

    public GetUsersQueryHandler(
        IUnitOfWork unitOfWork,
        IMapper mapper,
        ILogger<GetUsersQueryHandler> logger)
    {
        _unitOfWork = unitOfWork;
        _mapper = mapper;
        _logger = logger;
    }

    public async Task<Result<PagedResult<UserDto>>> Handle(GetUsersQuery request, CancellationToken cancellationToken)
    {
        try
        {
            var spec = new GetUsersSpecification(request.Role, request.IsActive, request.SearchTerm, request.SortBy, request.SortDescending);

            var pagedResult = await _unitOfWork.Users.GetPagedAsync(spec, request.Page, request.PageSize, cancellationToken);

            var userDtos = _mapper.Map<IReadOnlyList<UserDto>>(pagedResult.Data);

            var result = new PagedResult<UserDto>
            {
                Data = userDtos,
                TotalCount = pagedResult.TotalCount,
                Page = pagedResult.Page,
                PageSize = pagedResult.PageSize,
                TotalPages = pagedResult.TotalPages
            };

            return Result<PagedResult<UserDto>>.Success(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving users");
            return Result<PagedResult<UserDto>>.Failure("An error occurred while retrieving users");
        }
    }
}

// Supporting specification
public class GetUsersSpecification : BaseSpecification<User>
{
    public GetUsersSpecification(
        UserRole? role = null,
        bool? isActive = null,
        string? searchTerm = null,
        string? sortBy = null,
        bool sortDescending = false)
    {
        // Build criteria
        var criteria = PredicateBuilder.New<User>(true);

        if (role.HasValue)
            criteria = criteria.And(u => u.Role == role.Value);

        if (isActive.HasValue)
            criteria = criteria.And(u => u.IsActive == isActive.Value);

        if (!string.IsNullOrWhiteSpace(searchTerm))
        {
            var searchLower = searchTerm.ToLowerInvariant();
            criteria = criteria.And(u =>
                u.FirstName.ToLower().Contains(searchLower) ||
                u.LastName.ToLower().Contains(searchLower) ||
                u.Email.Value.Contains(searchLower));
        }

        SetCriteria(criteria);

        // Apply sorting
        switch (sortBy?.ToLowerInvariant())
        {
            case "firstname":
                if (sortDescending)
                    ApplyOrderByDescending(u => u.FirstName);
                else
                    ApplyOrderBy(u => u.FirstName);
                break;
            case "lastname":
                if (sortDescending)
                    ApplyOrderByDescending(u => u.LastName);
                else
                    ApplyOrderBy(u => u.LastName);
                break;
            case "email":
                if (sortDescending)
                    ApplyOrderByDescending(u => u.Email.Value);
                else
                    ApplyOrderBy(u => u.Email.Value);
                break;
            case "createdat":
                if (sortDescending)
                    ApplyOrderByDescending(u => u.CreatedAt);
                else
                    ApplyOrderBy(u => u.CreatedAt);
                break;
            default:
                ApplyOrderBy(u => u.LastName);
                ApplyThenBy(u => u.FirstName);
                break;
        }

        EnableNoTracking();
    }
}
```

#### **DTOs and Mapping (Application)**
```csharp
// DTOs
public class UserDto
{
    public int Id { get; set; }
    public string FirstName { get; set; } = string.Empty;
    public string LastName { get; set; } = string.Empty;
    public string FullName { get; set; } = string.Empty;
    public string Email { get; set; } = string.Empty;
    public UserRole Role { get; set; }
    public bool IsActive { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime? LastLoginAt { get; set; }
}

public class CreateUserDto
{
    public string FirstName { get; set; } = string.Empty;
    public string LastName { get; set; } = string.Empty;
    public string Email { get; set; } = string.Empty;
    public string Password { get; set; } = string.Empty;
    public UserRole Role { get; set; }
}

public class UpdateUserDto
{
    public string FirstName { get; set; } = string.Empty;
    public string LastName { get; set; } = string.Empty;
    public UserRole Role { get; set; }
    public bool IsActive { get; set; }
}

// AutoMapper Profile
public class MappingProfile : Profile
{
    public MappingProfile()
    {
        CreateMap<User, UserDto>()
            .ForMember(dest => dest.Email, opt => opt.MapFrom(src => src.Email.Value))
            .ForMember(dest => dest.FullName, opt => opt.MapFrom(src => src.FullName));

        CreateMap<CreateUserDto, CreateUserCommand>();
        CreateMap<UpdateUserDto, UpdateUserCommand>();

        CreateMap<Product, ProductDto>()
            .ForMember(dest => dest.Price, opt => opt.MapFrom(src => src.Price.Amount))
            .ForMember(dest => dest.Currency, opt => opt.MapFrom(src => src.Price.Currency));

        CreateMap<Order, OrderDto>()
            .ForMember(dest => dest.TotalAmount, opt => opt.MapFrom(src => src.TotalAmount.Amount))
            .ForMember(dest => dest.Currency, opt => opt.MapFrom(src => src.TotalAmount.Currency));
    }
}
```

#### **Behaviors (Application)**
```csharp
// Validation Behavior
public class ValidationBehavior<TRequest, TResponse> : IPipelineBehavior<TRequest, TResponse>
    where TRequest : IRequest<TResponse>
{
    private readonly IEnumerable<IValidator<TRequest>> _validators;
    private readonly ILogger<ValidationBehavior<TRequest, TResponse>> _logger;

    public ValidationBehavior(
        IEnumerable<IValidator<TRequest>> validators,
        ILogger<ValidationBehavior<TRequest, TResponse>> logger)
    {
        _validators = validators;
        _logger = logger;
    }

    public async Task<TResponse> Handle(TRequest request, RequestHandlerDelegate<TResponse> next, CancellationToken cancellationToken)
    {
        if (_validators.Any())
        {
            var context = new ValidationContext<TRequest>(request);
            var validationResults = await Task.WhenAll(_validators.Select(v => v.ValidateAsync(context, cancellationToken)));
            var failures = validationResults.SelectMany(r => r.Errors).Where(f => f != null).ToList();

            if (failures.Any())
            {
                _logger.LogWarning("Validation failed for {RequestType}: {Errors}",
                    typeof(TRequest).Name,
                    string.Join(", ", failures.Select(f => f.ErrorMessage)));

                throw new ValidationException(failures);
            }
        }

        return await next();
    }
}

// Logging Behavior
public class LoggingBehavior<TRequest, TResponse> : IPipelineBehavior<TRequest, TResponse>
    where TRequest : IRequest<TResponse>
{
    private readonly ILogger<LoggingBehavior<TRequest, TResponse>> _logger;
    private readonly ICurrentUserService _currentUserService;

    public LoggingBehavior(
        ILogger<LoggingBehavior<TRequest, TResponse>> logger,
        ICurrentUserService currentUserService)
    {
        _logger = logger;
        _currentUserService = currentUserService;
    }

    public async Task<TResponse> Handle(TRequest request, RequestHandlerDelegate<TResponse> next, CancellationToken cancellationToken)
    {
        var requestName = typeof(TRequest).Name;
        var userId = _currentUserService.UserId ?? "Anonymous";

        _logger.LogInformation("Handling {RequestName} for user {UserId}", requestName, userId);

        var stopwatch = Stopwatch.StartNew();
        try
        {
            var response = await next();
            stopwatch.Stop();

            _logger.LogInformation("Handled {RequestName} for user {UserId} in {ElapsedMilliseconds}ms",
                requestName, userId, stopwatch.ElapsedMilliseconds);

            return response;
        }
        catch (Exception ex)
        {
            stopwatch.Stop();
            _logger.LogError(ex, "Error handling {RequestName} for user {UserId} after {ElapsedMilliseconds}ms",
                requestName, userId, stopwatch.ElapsedMilliseconds);
            throw;
        }
    }
}

// Performance Behavior
public class PerformanceBehavior<TRequest, TResponse> : IPipelineBehavior<TRequest, TResponse>
    where TRequest : IRequest<TResponse>
{
    private readonly ILogger<PerformanceBehavior<TRequest, TResponse>> _logger;
    private readonly ICurrentUserService _currentUserService;
    private const int LongRunningRequestThreshold = 500; // milliseconds

    public PerformanceBehavior(
        ILogger<PerformanceBehavior<TRequest, TResponse>> logger,
        ICurrentUserService currentUserService)
    {
        _logger = logger;
        _currentUserService = currentUserService;
    }

    public async Task<TResponse> Handle(TRequest request, RequestHandlerDelegate<TResponse> next, CancellationToken cancellationToken)
    {
        var stopwatch = Stopwatch.StartNew();
        var response = await next();
        stopwatch.Stop();

        if (stopwatch.ElapsedMilliseconds > LongRunningRequestThreshold)
        {
            var requestName = typeof(TRequest).Name;
            var userId = _currentUserService.UserId ?? "Anonymous";

            _logger.LogWarning("Long running request detected: {RequestName} for user {UserId} took {ElapsedMilliseconds}ms",
                requestName, userId, stopwatch.ElapsedMilliseconds);
        }

        return response;
    }
}
```

### **API Layer Implementations**

#### **Base Controller (API)**
```csharp
[ApiController]
[Route("api/[controller]")]
[Produces("application/json")]
public abstract class BaseController : ControllerBase
{
    private ISender _mediator = null!;
    protected ISender Mediator => _mediator ??= HttpContext.RequestServices.GetRequiredService<ISender>();

    protected ActionResult HandleResult<T>(Result<T> result)
    {
        if (result.IsSuccess)
        {
            return result.Value is null ? NotFound() : Ok(result.Value);
        }

        return BadRequest(new ErrorResponse
        {
            Message = "Request failed",
            Errors = result.Errors
        });
    }

    protected ActionResult HandleResult(Result result)
    {
        if (result.IsSuccess)
        {
            return Ok();
        }

        return BadRequest(new ErrorResponse
        {
            Message = "Request failed",
            Errors = result.Errors
        });
    }

    protected ActionResult<ApiResponse<T>> HandleApiResponse<T>(Result<T> result)
    {
        if (result.IsSuccess)
        {
            return Ok(new ApiResponse<T>
            {
                Success = true,
                Data = result.Value,
                Message = "Request completed successfully"
            });
        }

        return BadRequest(new ApiResponse<T>
        {
            Success = false,
            Message = "Request failed",
            Errors = result.Errors
        });
    }
}
```

#### **Users Controller (API)**
```csharp
[Route("api/[controller]")]
public class UsersController : BaseController
{
    /// <summary>
    /// Get all users with optional filtering and pagination
    /// </summary>
    [HttpGet]
    [ProducesResponseType(typeof(ApiResponse<PagedResult<UserDto>>), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ErrorResponse), StatusCodes.Status400BadRequest)]
    public async Task<ActionResult<ApiResponse<PagedResult<UserDto>>>> GetUsers(
        [FromQuery] GetUsersQuery query)
    {
        var result = await Mediator.Send(query);
        return HandleApiResponse(result);
    }

    /// <summary>
    /// Get user by ID
    /// </summary>
    [HttpGet("{id:int}")]
    [ProducesResponseType(typeof(ApiResponse<UserDto>), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ErrorResponse), StatusCodes.Status404NotFound)]
    public async Task<ActionResult<ApiResponse<UserDto>>> GetUser(int id)
    {
        var query = new GetUserQuery { Id = id };
        var result = await Mediator.Send(query);
        return HandleApiResponse(result);
    }

    /// <summary>
    /// Create a new user
    /// </summary>
    [HttpPost]
    [ProducesResponseType(typeof(ApiResponse<int>), StatusCodes.Status201Created)]
    [ProducesResponseType(typeof(ErrorResponse), StatusCodes.Status400BadRequest)]
    public async Task<ActionResult<ApiResponse<int>>> CreateUser([FromBody] CreateUserDto dto)
    {
        var command = new CreateUserCommand
        {
            FirstName = dto.FirstName,
            LastName = dto.LastName,
            Email = dto.Email,
            Password = dto.Password,
            Role = dto.Role
        };

        var result = await Mediator.Send(command);

        if (result.IsSuccess)
        {
            return CreatedAtAction(nameof(GetUser), new { id = result.Value },
                new ApiResponse<int>
                {
                    Success = true,
                    Data = result.Value,
                    Message = "User created successfully"
                });
        }

        return HandleApiResponse(result);
    }

    /// <summary>
    /// Update an existing user
    /// </summary>
    [HttpPut("{id:int}")]
    [ProducesResponseType(StatusCodes.Status204NoContent)]
    [ProducesResponseType(typeof(ErrorResponse), StatusCodes.Status400BadRequest)]
    [ProducesResponseType(typeof(ErrorResponse), StatusCodes.Status404NotFound)]
    public async Task<ActionResult> UpdateUser(int id, [FromBody] UpdateUserDto dto)
    {
        var command = new UpdateUserCommand
        {
            Id = id,
            FirstName = dto.FirstName,
            LastName = dto.LastName,
            Role = dto.Role,
            IsActive = dto.IsActive
        };

        var result = await Mediator.Send(command);
        return HandleResult(result);
    }

    /// <summary>
    /// Delete a user
    /// </summary>
    [HttpDelete("{id:int}")]
    [ProducesResponseType(StatusCodes.Status204NoContent)]
    [ProducesResponseType(typeof(ErrorResponse), StatusCodes.Status404NotFound)]
    public async Task<ActionResult> DeleteUser(int id)
    {
        var command = new DeleteUserCommand { Id = id };
        var result = await Mediator.Send(command);
        return HandleResult(result);
    }
}
```

## Benefits of This Structure:

1. **Separation of Concerns**: Each layer has a single responsibility
2. **Testability**: Easy to mock interfaces and test business logic
3. **Flexibility**: Swap implementations without changing business logic
4. **Reusability**: Generic repository and specifications can be reused
5. **Performance**: Specifications allow complex queries without N+1 problems
6. **Maintainability**: Changes in outer layers don't affect inner layers
7. **Query Optimization**: SpecificationEvaluator builds efficient EF queries
8. **CQRS Pattern**: Clear separation between commands and queries
9. **Domain Events**: Loose coupling between domain entities and side effects
10. **Validation**: Centralized validation using FluentValidation
11. **Logging & Monitoring**: Built-in logging and performance monitoring
12. **Error Handling**: Consistent error handling across all layers
13. **Caching**: Built-in caching support with cache invalidation
14. **Security**: Role-based authorization and audit trails
15. **Scalability**: Horizontal scaling support with proper abstractions

## Additional Patterns Implemented:

- **Repository Pattern**: Data access abstraction
- **Unit of Work Pattern**: Transaction management
- **Specification Pattern**: Complex query building
- **CQRS Pattern**: Command Query Responsibility Segregation
- **Mediator Pattern**: Decoupled request/response handling
- **Domain Events**: Domain-driven design events
- **Result Pattern**: Functional error handling
- **Pipeline Behaviors**: Cross-cutting concerns
- **Value Objects**: Domain modeling
- **Soft Delete**: Data preservation
- **Audit Trail**: Change tracking
- **Pagination**: Efficient data retrieval

This architecture provides a solid foundation for enterprise applications with clean separation of concerns, maximum testability, and excellent maintainability.