using MediatR;
using Microsoft.Extensions.Logging;
using SohApp.Application.Common;
using SohApp.Core.Entities;
using SohApp.Core.Interfaces;
using SohApp.Core.ValueObjects;

namespace SohApp.Application.Features.Products.Commands.CreateProduct;

public class CreateProductCommandHandler : IRequestHandler<CreateProductCommand, Result<int>>
{
    private readonly IUnitOfWork _unitOfWork;
    private readonly ILogger<CreateProductCommandHandler> _logger;

    public CreateProductCommandHandler(
        IUnitOfWork unitOfWork,
        ILogger<CreateProductCommandHandler> logger)
    {
        _unitOfWork = unitOfWork;
        _logger = logger;
    }

    public async Task<Result<int>> Handle(CreateProductCommand request, CancellationToken cancellationToken)
    {
        try
        {
            // Check if category exists
            var categoryExists = await _unitOfWork.Categories.ExistsAsync(request.CategoryId, cancellationToken);
            if (!categoryExists)
            {
                return Result<int>.Failure("Category not found");
            }

            // Check if SKU is unique
            var existingProduct = await _unitOfWork.Products.GetBySkuAsync(request.Sku, cancellationToken);
            if (existingProduct != null)
            {
                return Result<int>.Failure("SKU already exists");
            }

            // Create product entity
            var product = new Product
            {
                Name = request.Name,
                Description = request.Description,
                Sku = request.Sku,
                Price = new Money(request.Price, request.Currency),
                CategoryId = request.CategoryId,
                IsActive = true
            };

            // Set initial stock using the business method
            product.SetStock(request.StockQuantity);

            // Save to database
            await _unitOfWork.Products.AddAsync(product, cancellationToken);
            await _unitOfWork.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("Product created successfully with ID: {ProductId}", product.Id);

            return Result<int>.Success(product.Id);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating product with SKU: {Sku}", request.Sku);
            return Result<int>.Failure("An error occurred while creating the product");
        }
    }
}
