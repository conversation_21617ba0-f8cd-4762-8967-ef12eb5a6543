using AutoMapper;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.RateLimiting;
using SohApp.API.Controllers.Base;
using SohApp.API.Models;
using SohApp.Application.Common;
using SohApp.Application.DTOs.User;
using SohApp.Application.Features.Users.Commands.CreateUser;
using SohApp.Application.Features.Users.Commands.UpdateUser;
using SohApp.Application.Features.Users.Commands.DeleteUser;
using SohApp.Application.Features.Users.Queries.GetUser;
using SohApp.Application.Features.Users.Queries.GetUsers;
using System.ComponentModel.DataAnnotations;

namespace SohApp.API.Controllers;

[Route("api/[controller]")]
public class UsersController : BaseController
{
    private readonly IMapper _mapper;

    public UsersController(IMapper mapper)
    {
        _mapper = mapper;
    }
    /// <summary>
    /// Get all users with optional filtering and pagination
    /// </summary>
    [HttpGet]
    [ProducesResponseType(typeof(ApiResponse<PagedResult<UserDto>>), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ErrorResponse), StatusCodes.Status400BadRequest)]
    [ProducesResponseType(typeof(ErrorResponse), StatusCodes.Status401Unauthorized)]
    public async Task<ActionResult<ApiResponse<PagedResult<UserDto>>>> GetUsers(
        [FromQuery] GetUsersQuery query,
        CancellationToken cancellationToken = default)
    {
        var result = await Mediator.Send(query, cancellationToken);
        return HandleApiResponse(result);
    }

    /// <summary>
    /// Get user by ID
    /// </summary>
    [HttpGet("{id:int:min(1)}")]
    [ProducesResponseType(typeof(ApiResponse<UserDto>), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ErrorResponse), StatusCodes.Status400BadRequest)]
    [ProducesResponseType(typeof(ErrorResponse), StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(typeof(ErrorResponse), StatusCodes.Status404NotFound)]
    public async Task<ActionResult<ApiResponse<UserDto>>> GetUser(
        [Range(1, int.MaxValue)] int id,
        CancellationToken cancellationToken = default)
    {
        var query = new GetUserQuery { Id = id };
        var result = await Mediator.Send(query, cancellationToken);
        return HandleApiResponse(result);
    }

    /// <summary>
    /// Create a new user
    /// </summary>
    [HttpPost]
    [EnableRateLimiting("CreateUserPolicy")] // Rate limit user creation
    [ProducesResponseType(typeof(ApiResponse<int>), StatusCodes.Status201Created)]
    [ProducesResponseType(typeof(ErrorResponse), StatusCodes.Status400BadRequest)]
    [ProducesResponseType(typeof(ErrorResponse), StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(typeof(ErrorResponse), StatusCodes.Status409Conflict)]
    [ProducesResponseType(typeof(ErrorResponse), StatusCodes.Status422UnprocessableEntity)]
    public async Task<ActionResult<ApiResponse<int>>> CreateUser(
        [FromBody, Required] CreateUserDto dto,
        CancellationToken cancellationToken = default)
    {
        // Use AutoMapper for cleaner mapping
        var command = _mapper.Map<CreateUserCommand>(dto);

        var result = await Mediator.Send(command, cancellationToken);

        if (result.IsSuccess)
        {
            return CreatedAtActionWithApiResponse(nameof(GetUser), new { id = result.Value }, result);
        }

        return HandleApiResponse(result);
    }

    /// <summary>
    /// Update an existing user
    /// </summary>
    [HttpPut("{id:int:min(1)}")]
    [EnableRateLimiting("UpdateUserPolicy")] // Rate limit user updates
    [ProducesResponseType(StatusCodes.Status204NoContent)]
    [ProducesResponseType(typeof(ErrorResponse), StatusCodes.Status400BadRequest)]
    [ProducesResponseType(typeof(ErrorResponse), StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(typeof(ErrorResponse), StatusCodes.Status404NotFound)]
    [ProducesResponseType(typeof(ErrorResponse), StatusCodes.Status422UnprocessableEntity)]
    public async Task<ActionResult> UpdateUser(
        [Range(1, int.MaxValue)] int id,
        [FromBody, Required] UpdateUserDto dto,
        CancellationToken cancellationToken = default)
    {
        // Use AutoMapper and set the ID
        var command = _mapper.Map<UpdateUserCommand>(dto);
        command.Id = id;

        var result = await Mediator.Send(command, cancellationToken);
        return HandleResult(result);
    }

    /// <summary>
    /// Delete a user (soft delete)
    /// </summary>
    [HttpDelete("{id:int:min(1)}")]
    [EnableRateLimiting("DeleteUserPolicy")] // Rate limit user deletions
    [Authorize(Roles = "Admin")] // Only admins can delete users
    [ProducesResponseType(StatusCodes.Status204NoContent)]
    [ProducesResponseType(typeof(ErrorResponse), StatusCodes.Status400BadRequest)]
    [ProducesResponseType(typeof(ErrorResponse), StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(typeof(ErrorResponse), StatusCodes.Status403Forbidden)]
    [ProducesResponseType(typeof(ErrorResponse), StatusCodes.Status404NotFound)]
    public async Task<ActionResult> DeleteUser(
        [Range(1, int.MaxValue)] int id,
        CancellationToken cancellationToken = default)
    {
        var command = new DeleteUserCommand { Id = id };
        var result = await Mediator.Send(command, cancellationToken);
        return HandleResult(result);
    }
}
