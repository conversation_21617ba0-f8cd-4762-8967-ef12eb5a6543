using SohApp.Core.Events.Base;

namespace SohApp.Core.Events.ProductEvents;

public class ProductLowStockEvent : DomainEvent
{
    public int ProductId { get; }
    public string ProductName { get; }
    public int CurrentStockQuantity { get; }

    public ProductLowStockEvent(int productId, string productName, int currentStockQuantity)
    {
        ProductId = productId;
        ProductName = productName;
        CurrentStockQuantity = currentStockQuantity;
    }
}
