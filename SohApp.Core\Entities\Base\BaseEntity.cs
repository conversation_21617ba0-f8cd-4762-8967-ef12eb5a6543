using System.Collections.Concurrent;
using SohApp.Core.Events.Base;

namespace SohApp.Core.Entities.Base;

public abstract class BaseEntity : IAuditableEntity, ISoftDeletable
{
    public int Id { get; set; }

    // Fixed: Don't use DateTime.UtcNow in property initializer - causes performance issues
    // This will be set by the DbContext interceptor
    public DateTime CreatedAt { get; set; }
    public DateTime? UpdatedAt { get; set; }
    public string CreatedBy { get; set; } = string.Empty;
    public string? UpdatedBy { get; set; }
    public bool IsDeleted { get; set; }
    public DateTime? DeletedAt { get; set; }

    // Fixed: Use thread-safe collection for domain events
    private readonly ConcurrentBag<IDomainEvent> _domainEvents = new();
    public IReadOnlyCollection<IDomainEvent> DomainEvents => _domainEvents.ToList().AsReadOnly();

    /// <summary>
    /// Adds a domain event to be dispatched when the entity is saved
    /// </summary>
    /// <param name="domainEvent">The domain event to add</param>
    /// <exception cref="ArgumentNullException">Thrown when domainEvent is null</exception>
    public void AddDomainEvent(IDomainEvent domainEvent)
    {
        ArgumentNullException.ThrowIfNull(domainEvent);
        _domainEvents.Add(domainEvent);
    }

    /// <summary>
    /// Removes a specific domain event
    /// </summary>
    /// <param name="domainEvent">The domain event to remove</param>
    public void RemoveDomainEvent(IDomainEvent domainEvent)
    {
        if (domainEvent == null) return;

        // Convert to list for removal operation since ConcurrentBag doesn't support direct removal
        var events = _domainEvents.ToList();
        _domainEvents.Clear();

        foreach (var evt in events.Where(e => !e.Equals(domainEvent)))
        {
            _domainEvents.Add(evt);
        }
    }

    /// <summary>
    /// Clears all domain events
    /// </summary>
    public void ClearDomainEvents()
    {
        // Clear the concurrent bag
        while (!_domainEvents.IsEmpty)
        {
            _domainEvents.TryTake(out _);
        }
    }

    /// <summary>
    /// Checks if the entity has any pending domain events
    /// </summary>
    public bool HasDomainEvents => !_domainEvents.IsEmpty;
}
