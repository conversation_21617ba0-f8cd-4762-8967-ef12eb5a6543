using Microsoft.EntityFrameworkCore;
using SohApp.Core.Entities;
using SohApp.Core.Enums;
using SohApp.Core.Specifications.Base;

namespace SohApp.Application.Features.Orders.Queries.GetOrders.Specifications;

public class GetOrdersSpecification : BaseSpecification<Order>
{
    public GetOrdersSpecification(
        int? userId = null,
        OrderStatus? status = null,
        DateTime? startDate = null,
        DateTime? endDate = null,
        string? searchTerm = null,
        string? sortBy = null,
        bool sortDescending = true)
    {
        // Build criteria
        SetCriteria(o => !o.IsDeleted);

        if (userId.HasValue)
        {
            var currentCriteria = Criteria;
            SetCriteria(o => currentCriteria!.Compile()(o) && o.UserId == userId.Value);
        }

        if (status.HasValue)
        {
            var currentCriteria = Criteria;
            SetCriteria(o => currentCriteria!.Compile()(o) && o.Status == status.Value);
        }

        if (startDate.HasValue)
        {
            var currentCriteria = Criteria;
            SetCriteria(o => currentCriteria!.Compile()(o) && o.CreatedAt >= startDate.Value);
        }

        if (endDate.HasValue)
        {
            var currentCriteria = Criteria;
            SetCriteria(o => currentCriteria!.Compile()(o) && o.CreatedAt <= endDate.Value);
        }

        if (!string.IsNullOrWhiteSpace(searchTerm))
        {
            var searchLower = searchTerm.ToLowerInvariant();
            var currentCriteria = Criteria;
            // For search, we'll focus on searchable fields that don't cause EF translation issues
            SetCriteria(o => currentCriteria!.Compile()(o) &&
                (o.OrderNumber.ToLower().Contains(searchLower) ||
                 o.User.FirstName.ToLower().Contains(searchLower) ||
                 o.User.LastName.ToLower().Contains(searchLower)));

            // Note: Email search would require a custom specification or raw SQL
            // For now, we'll focus on order number and user name search
        }

        // Include related data
        AddInclude(o => o.User);
        AddInclude(o => o.OrderItems);

        // Apply sorting
        switch (sortBy?.ToLowerInvariant())
        {
            case "ordernumber":
                if (sortDescending)
                    ApplyOrderByDescending(o => o.OrderNumber);
                else
                    ApplyOrderBy(o => o.OrderNumber);
                break;
            case "status":
                if (sortDescending)
                    ApplyOrderByDescending(o => o.Status);
                else
                    ApplyOrderBy(o => o.Status);
                break;
            case "totalamount":
                if (sortDescending)
                    ApplyOrderByDescending(o => o.TotalAmount.Amount);
                else
                    ApplyOrderBy(o => o.TotalAmount.Amount);
                break;
            case "user":
                if (sortDescending)
                    ApplyOrderByDescending(o => o.User.LastName);
                else
                    ApplyOrderBy(o => o.User.LastName);
                ApplyThenBy(o => o.User.FirstName);
                break;
            default: // Default to CreatedAt
                if (sortDescending)
                    ApplyOrderByDescending(o => o.CreatedAt);
                else
                    ApplyOrderBy(o => o.CreatedAt);
                break;
        }

        EnableNoTracking();
    }
}
