using MediatR;
using Microsoft.AspNetCore.Mvc;
using SohApp.API.Models;
using SohApp.Application.Common;
using SohApp.Core.Exceptions;

namespace SohApp.API.Controllers.Base;

[ApiController]
[Route("api/[controller]")]
[Produces("application/json")]
public abstract class BaseController : ControllerBase
{
    private readonly ILogger<BaseController>? _logger;
    private ISender? _mediator;

    // New constructor with dependency injection
    protected BaseController(ISender mediator, ILogger<BaseController> logger)
    {
        _mediator = mediator;
        _logger = logger;
    }

    // Backward compatible constructor
    protected BaseController()
    {
        _logger = null;
        _mediator = null;
    }

    protected ISender Mediator => _mediator ??= HttpContext.RequestServices.GetRequiredService<ISender>();

    protected ActionResult HandleResult<T>(Result<T> result)
    {
        if (result.IsSuccess)
        {
            return result.Value is null ? NotFound() : Ok(result.Value);
        }

        return HandleErrorResult(result);
    }

    protected ActionResult HandleResult(Result result)
    {
        if (result.IsSuccess)
        {
            return Ok();
        }

        return HandleErrorResult(result);
    }

    private ActionResult HandleErrorResult(Result result)
    {
        // Log the error for monitoring (only if logger is available)
        _logger?.LogWarning("Request failed with errors: {Errors}", string.Join(", ", result.Errors));

        // Determine appropriate status code based on error type
        var statusCode = DetermineStatusCode(result.Errors);

        var errorResponse = new ErrorResponse
        {
            Message = GetUserFriendlyMessage(statusCode),
            Errors = SanitizeErrors(result.Errors),
            TraceId = HttpContext.TraceIdentifier
        };

        return statusCode switch
        {
            400 => BadRequest(errorResponse),
            401 => Unauthorized(errorResponse),
            403 => Forbid(),
            404 => NotFound(errorResponse),
            409 => Conflict(errorResponse),
            422 => UnprocessableEntity(errorResponse),
            _ => BadRequest(errorResponse)
        };
    }

    private static int DetermineStatusCode(IEnumerable<string> errors)
    {
        var errorList = errors.ToList();

        // Check for specific error patterns
        if (errorList.Any(e => e.Contains("not found", StringComparison.OrdinalIgnoreCase)))
            return 404;

        if (errorList.Any(e => e.Contains("already exists", StringComparison.OrdinalIgnoreCase)))
            return 409;

        if (errorList.Any(e => e.Contains("unauthorized", StringComparison.OrdinalIgnoreCase)))
            return 401;

        if (errorList.Any(e => e.Contains("forbidden", StringComparison.OrdinalIgnoreCase)))
            return 403;

        if (errorList.Any(e => e.Contains("validation", StringComparison.OrdinalIgnoreCase)))
            return 422;

        return 400; // Default to bad request
    }

    private static string GetUserFriendlyMessage(int statusCode)
    {
        return statusCode switch
        {
            400 => "The request was invalid",
            401 => "Authentication is required",
            403 => "Access is forbidden",
            404 => "The requested resource was not found",
            409 => "The request conflicts with the current state",
            422 => "The request contains validation errors",
            _ => "An error occurred while processing the request"
        };
    }

    private static IEnumerable<string> SanitizeErrors(IEnumerable<string> errors)
    {
        // Remove sensitive information from error messages
        return errors.Select(error =>
        {
            // Remove stack traces, connection strings, etc.
            if (error.Contains("Exception") || error.Contains("at ") || error.Contains("ConnectionString"))
            {
                return "An internal error occurred";
            }
            return error;
        });
    }

    protected ActionResult<ApiResponse<T>> HandleApiResponse<T>(Result<T> result)
    {
        if (result.IsSuccess)
        {
            return Ok(new ApiResponse<T>
            {
                Success = true,
                Data = result.Value,
                Message = "Request completed successfully"
            });
        }

        return BadRequest(new ApiResponse<T>
        {
            Success = false,
            Message = "Request failed",
            Errors = result.Errors
        });
    }

    protected CreatedAtActionResult CreatedAtActionWithApiResponse<T>(string actionName, object routeValues, Result<T> result)
    {
        return CreatedAtAction(actionName, routeValues, new ApiResponse<T>
        {
            Success = true,
            Data = result.Value,
            Message = "Resource created successfully"
        });
    }
}
