using Microsoft.AspNetCore.Mvc;
using SohApp.API.Controllers.Base;
using SohApp.API.Models;
using SohApp.Application.Common;
using SohApp.Application.DTOs.Product;
using SohApp.Application.Features.Products.Commands.CreateProduct;
using SohApp.Application.Features.Products.Queries.GetProduct;
using SohApp.Application.Features.Products.Queries.GetProducts;

namespace SohApp.API.Controllers;

[Route("api/[controller]")]
public class ProductsController : BaseController
{
    /// <summary>
    /// Get all products with optional filtering and pagination
    /// </summary>
    [HttpGet]
    [ProducesResponseType(typeof(ApiResponse<PagedResult<ProductDto>>), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ErrorResponse), StatusCodes.Status400BadRequest)]
    public async Task<ActionResult<ApiResponse<PagedResult<ProductDto>>>> GetProducts(
        [FromQuery] GetProductsQuery query)
    {
        var result = await Mediator.Send(query);
        return HandleApiResponse(result);
    }

    /// <summary>
    /// Get product by ID
    /// </summary>
    [HttpGet("{id:int}")]
    [ProducesResponseType(typeof(ApiResponse<ProductDto>), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ErrorResponse), StatusCodes.Status404NotFound)]
    public async Task<ActionResult<ApiResponse<ProductDto>>> GetProduct(int id)
    {
        var query = new GetProductQuery { Id = id };
        var result = await Mediator.Send(query);
        return HandleApiResponse(result);
    }

    /// <summary>
    /// Create a new product
    /// </summary>
    [HttpPost]
    [ProducesResponseType(typeof(ApiResponse<int>), StatusCodes.Status201Created)]
    [ProducesResponseType(typeof(ErrorResponse), StatusCodes.Status400BadRequest)]
    public async Task<ActionResult<ApiResponse<int>>> CreateProduct([FromBody] CreateProductDto dto)
    {
        var command = new CreateProductCommand
        {
            Name = dto.Name,
            Description = dto.Description,
            Sku = dto.Sku,
            Price = dto.Price,
            Currency = dto.Currency,
            StockQuantity = dto.StockQuantity,
            CategoryId = dto.CategoryId
        };

        var result = await Mediator.Send(command);
        
        if (result.IsSuccess)
        {
            return CreatedAtActionWithApiResponse(nameof(GetProduct), new { id = result.Value }, result);
        }

        return HandleApiResponse(result);
    }
}
