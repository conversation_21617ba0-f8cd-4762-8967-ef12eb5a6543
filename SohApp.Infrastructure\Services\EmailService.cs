using Microsoft.Extensions.Logging;
using SohApp.Core.Interfaces.Services;

namespace SohApp.Infrastructure.Services;

public class EmailService : IEmailService
{
    private readonly ILogger<EmailService> _logger;

    public EmailService(ILogger<EmailService> logger)
    {
        _logger = logger;
    }

    public async Task SendEmailAsync(string to, string subject, string body, CancellationToken cancellationToken = default)
    {
        // TODO: Implement actual email sending logic (SMTP, SendGrid, etc.)
        _logger.LogInformation("Sending email to {To} with subject: {Subject}", to, subject);
        
        // Simulate async operation
        await Task.Delay(100, cancellationToken);
        
        _logger.LogInformation("Email sent successfully to {To}", to);
    }

    public async Task SendEmailAsync(IEnumerable<string> to, string subject, string body, CancellationToken cancellationToken = default)
    {
        var recipients = to.ToList();
        _logger.LogInformation("Sending email to {Count} recipients with subject: {Subject}", recipients.Count, subject);
        
        foreach (var recipient in recipients)
        {
            await SendEmailAsync(recipient, subject, body, cancellationToken);
        }
    }

    public async Task SendWelcomeEmailAsync(string to, string userName, CancellationToken cancellationToken = default)
    {
        var subject = "Welcome to SohApp!";
        var body = $"Hello {userName},\n\nWelcome to SohApp! We're excited to have you on board.\n\nBest regards,\nThe SohApp Team";
        
        await SendEmailAsync(to, subject, body, cancellationToken);
    }

    public async Task SendOrderConfirmationEmailAsync(string to, string orderNumber, decimal totalAmount, CancellationToken cancellationToken = default)
    {
        var subject = $"Order Confirmation - {orderNumber}";
        var body = $"Thank you for your order!\n\nOrder Number: {orderNumber}\nTotal Amount: ${totalAmount:F2}\n\nWe'll send you another email when your order ships.\n\nBest regards,\nThe SohApp Team";
        
        await SendEmailAsync(to, subject, body, cancellationToken);
    }
}
