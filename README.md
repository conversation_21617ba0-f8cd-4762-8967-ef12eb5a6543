# SohApp - Clean Architecture .NET Solution

A complete .NET 9 solution implementing Clean Architecture principles with CQRS, Repository Pattern, Unit of Work, and Specification Pattern.

## Architecture Overview

```
API → Application → Core ← Infrastructure
API → Infrastructure (for DI registration only)
```

### Projects Structure

- **SohApp.Core** - Domain entities, value objects, interfaces, specifications, and domain events
- **SohApp.Application** - CQRS commands/queries, DTOs, behaviors, and application services
- **SohApp.Infrastructure** - Data access, repositories, external services, and infrastructure implementations
- **SohApp.API** - Controllers, middleware, and API-specific configurations

## Features Implemented

### Core/Domain Layer
- ✅ Base entities with audit trails and soft delete
- ✅ Domain events system
- ✅ Value objects (Email, Money, Address)
- ✅ Rich domain entities (User, Product, Order, Category)
- ✅ Specification pattern for complex queries
- ✅ Domain exceptions and business rules

### Application Layer
- ✅ CQRS with MediatR
- ✅ Command and Query handlers
- ✅ FluentValidation for input validation
- ✅ AutoMapper for object mapping
- ✅ Pipeline behaviors (Validation, Logging, Performance)
- ✅ Domain event handlers
- ✅ Result pattern for error handling

### Infrastructure Layer
- ✅ Entity Framework Core with SQL Server
- ✅ Generic Repository implementation
- ✅ Unit of Work pattern
- ✅ Specification evaluator
- ✅ Database configurations with Fluent API
- ✅ Current user service
- ✅ Email service (mock implementation)
- ✅ Domain event dispatcher

### API Layer
- ✅ RESTful controllers
- ✅ Swagger/OpenAPI documentation
- ✅ Global exception handling middleware
- ✅ Request/Response logging
- ✅ CORS configuration
- ✅ Health checks

## Getting Started

### Prerequisites
- .NET 9 SDK
- SQL Server or SQL Server LocalDB
- Visual Studio 2022 or VS Code

### Setup Instructions

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd SohApp
   ```

2. **Update connection string**
   Update the connection string in `SohApp.API/appsettings.json`:
   ```json
   {
     "ConnectionStrings": {
       "DefaultConnection": "Server=(localdb)\\mssqllocaldb;Database=SohAppDb;Trusted_Connection=true;MultipleActiveResultSets=true"
     }
   }
   ```

3. **Create and run database migrations**
   ```bash
   dotnet ef migrations add InitialCreate --project SohApp.Infrastructure --startup-project SohApp.API
   dotnet ef database update --project SohApp.Infrastructure --startup-project SohApp.API
   ```

4. **Build and run the solution**
   ```bash
   dotnet build
   dotnet run --project SohApp.API
   ```

5. **Access the API**
   - API: `https://localhost:7000` or `http://localhost:5000`
   - Swagger UI: `https://localhost:7000` (in development)

## API Endpoints

### Users
- `GET /api/users` - Get all users with pagination and filtering
- `GET /api/users/{id}` - Get user by ID
- `POST /api/users` - Create new user
- `PUT /api/users/{id}` - Update user
- `DELETE /api/users/{id}` - Delete user

### Products
- `GET /api/products` - Get all products with pagination and filtering
- `GET /api/products/{id}` - Get product by ID
- `POST /api/products` - Create new product

## Design Patterns Implemented

1. **Clean Architecture** - Dependency inversion and separation of concerns
2. **CQRS** - Command Query Responsibility Segregation
3. **Repository Pattern** - Data access abstraction
4. **Unit of Work** - Transaction management
5. **Specification Pattern** - Complex query building
6. **Domain Events** - Loose coupling between domain entities
7. **Result Pattern** - Functional error handling
8. **Mediator Pattern** - Decoupled request/response handling
9. **Pipeline Behaviors** - Cross-cutting concerns
10. **Value Objects** - Domain modeling

## Technologies Used

- **.NET 9** - Framework
- **Entity Framework Core** - ORM
- **SQL Server** - Database
- **MediatR** - CQRS and Mediator pattern
- **AutoMapper** - Object mapping
- **FluentValidation** - Input validation
- **Swashbuckle** - API documentation
- **BCrypt.Net** - Password hashing

## Project Status

✅ **Complete** - The solution is fully functional with all major patterns implemented.

### What's Included
- Complete Clean Architecture structure
- Working CRUD operations for Users and Products
- Database integration with Entity Framework Core
- Comprehensive error handling and validation
- API documentation with Swagger
- Domain events and email notifications
- Audit trails and soft delete functionality

### Next Steps (Optional Enhancements)
- Add authentication and authorization (JWT)
- Implement caching with Redis
- Add integration tests
- Implement background jobs with Hangfire
- Add API versioning
- Implement rate limiting
- Add comprehensive logging with Serilog

## Contributing

This is a demonstration project showcasing Clean Architecture principles in .NET. Feel free to use it as a reference or starting point for your own projects.
