using SohApp.Core.Entities.Base;
using SohApp.Core.Interfaces.Repositories;

namespace SohApp.Core.Interfaces;

public interface IUnitOfWork : IDisposable
{
    // Repository properties
    IUserRepository Users { get; }
    IProductRepository Products { get; }
    IOrderRepository Orders { get; }
    ICategoryRepository Categories { get; }
    IRefreshTokenRepository RefreshTokens { get; }
    
    // Transaction methods
    Task<int> SaveChangesAsync(CancellationToken cancellationToken = default);
    Task<int> SaveChangesAsync(string userId, CancellationToken cancellationToken = default);
    Task<int> SaveChangesWithoutEventsAsync(CancellationToken cancellationToken = default);
    Task BeginTransactionAsync(CancellationToken cancellationToken = default);
    Task CommitTransactionAsync(CancellationToken cancellationToken = default);
    Task RollbackTransactionAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Executes a function within a transaction, handling retrying execution strategies properly
    /// </summary>
    Task<T> ExecuteInTransactionAsync<T>(Func<Task<T>> operation, CancellationToken cancellationToken = default);

    /// <summary>
    /// Executes an action within a transaction, handling retrying execution strategies properly
    /// </summary>
    Task ExecuteInTransactionAsync(Func<Task> operation, CancellationToken cancellationToken = default);
    
    // Generic repository access
    IGenericRepository<T> Repository<T>() where T : BaseEntity;
    
    // Domain events
    Task PublishDomainEventsAsync(CancellationToken cancellationToken = default);
}
