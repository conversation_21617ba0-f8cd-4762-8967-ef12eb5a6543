using FluentValidation;
using SohApp.Core.Constants;

namespace SohApp.Application.Features.Users.Commands.UpdateUser;

public class UpdateUserCommandValidator : AbstractValidator<UpdateUserCommand>
{
    public UpdateUserCommandValidator()
    {
        RuleFor(x => x.Id)
            .GreaterThan(0).WithMessage("User ID must be greater than zero");

        RuleFor(x => x.FirstName)
            .NotEmpty().WithMessage("First name is required")
            .MaximumLength(DomainConstants.User.FirstNameMaxLength).WithMessage($"First name must not exceed {DomainConstants.User.FirstNameMaxLength} characters");

        RuleFor(x => x.LastName)
            .NotEmpty().WithMessage("Last name is required")
            .MaximumLength(DomainConstants.User.LastNameMaxLength).WithMessage($"Last name must not exceed {DomainConstants.User.LastNameMaxLength} characters");

        RuleFor(x => x.Role)
            .IsInEnum().WithMessage("Invalid user role");
    }
}
