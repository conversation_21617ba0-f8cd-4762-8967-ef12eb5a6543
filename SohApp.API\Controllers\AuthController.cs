using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.RateLimiting;
using SohApp.API.Controllers.Base;
using SohApp.API.Models;
using SohApp.Application.DTOs.Auth;
using SohApp.Application.Interfaces;
using System.ComponentModel.DataAnnotations;
using System.Security.Claims;

namespace SohApp.API.Controllers;

[ApiController]
[Route("api/[controller]")]
[EnableRateLimiting("AuthPolicy")]
public class AuthController : BaseController
{
    private readonly IAuthenticationService _authenticationService;

    public AuthController(IAuthenticationService authenticationService)
    {
        _authenticationService = authenticationService;
    }

    /// <summary>
    /// Authenticate user and return JWT tokens
    /// </summary>
    /// <param name="loginDto">Login credentials</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Login response with access and refresh tokens</returns>
    [HttpPost("login")]
    [AllowAnonymous]
    [ProducesResponseType(typeof(ApiResponse<LoginResponseDto>), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ErrorResponse), StatusCodes.Status400BadRequest)]
    [ProducesResponseType(typeof(ErrorResponse), StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(typeof(ErrorResponse), StatusCodes.Status422UnprocessableEntity)]
    [ProducesResponseType(typeof(ErrorResponse), StatusCodes.Status429TooManyRequests)]
    public async Task<ActionResult<ApiResponse<LoginResponseDto>>> Login(
        [FromBody, Required] LoginDto loginDto,
        CancellationToken cancellationToken = default)
    {
        var ipAddress = GetClientIpAddress();
        var result = await _authenticationService.LoginAsync(loginDto, ipAddress, cancellationToken);
        return HandleApiResponse(result);
    }

    /// <summary>
    /// Refresh access token using refresh token
    /// </summary>
    /// <param name="refreshTokenDto">Refresh token</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>New access and refresh tokens</returns>
    [HttpPost("refresh")]
    [AllowAnonymous]
    [ProducesResponseType(typeof(ApiResponse<RefreshTokenResponseDto>), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ErrorResponse), StatusCodes.Status400BadRequest)]
    [ProducesResponseType(typeof(ErrorResponse), StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(typeof(ErrorResponse), StatusCodes.Status422UnprocessableEntity)]
    [ProducesResponseType(typeof(ErrorResponse), StatusCodes.Status429TooManyRequests)]
    public async Task<ActionResult<ApiResponse<RefreshTokenResponseDto>>> RefreshToken(
        [FromBody, Required] RefreshTokenDto refreshTokenDto,
        CancellationToken cancellationToken = default)
    {
        var ipAddress = GetClientIpAddress();
        var result = await _authenticationService.RefreshTokenAsync(refreshTokenDto, ipAddress, cancellationToken);
        return HandleApiResponse(result);
    }

    /// <summary>
    /// Revoke a specific refresh token
    /// </summary>
    /// <param name="refreshTokenDto">Refresh token to revoke</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Success response</returns>
    [HttpPost("revoke")]
    [Authorize]
    [ProducesResponseType(typeof(ApiResponse), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ErrorResponse), StatusCodes.Status400BadRequest)]
    [ProducesResponseType(typeof(ErrorResponse), StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(typeof(ErrorResponse), StatusCodes.Status422UnprocessableEntity)]
    [ProducesResponseType(typeof(ErrorResponse), StatusCodes.Status429TooManyRequests)]
    public async Task<ActionResult<ApiResponse>> RevokeToken(
        [FromBody, Required] RefreshTokenDto refreshTokenDto,
        CancellationToken cancellationToken = default)
    {
        var ipAddress = GetClientIpAddress();
        var result = await _authenticationService.RevokeTokenAsync(refreshTokenDto, ipAddress, cancellationToken);
        return HandleApiResponse(result);
    }

    /// <summary>
    /// Revoke all refresh tokens for the current user (logout from all devices)
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Success response</returns>
    [HttpPost("revoke-all")]
    [Authorize]
    [ProducesResponseType(typeof(ApiResponse), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ErrorResponse), StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(typeof(ErrorResponse), StatusCodes.Status429TooManyRequests)]
    public async Task<ActionResult<ApiResponse>> RevokeAllTokens(CancellationToken cancellationToken = default)
    {
        var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
        if (!int.TryParse(userIdClaim, out var userId))
        {
            return HandleApiResponse(Application.Common.Result.Failure("Invalid user ID"));
        }

        var ipAddress = GetClientIpAddress();
        var result = await _authenticationService.RevokeAllTokensAsync(userId, ipAddress, cancellationToken);
        return HandleApiResponse(result);
    }

    /// <summary>
    /// Get current user information from JWT token
    /// </summary>
    /// <returns>Current user information</returns>
    [HttpGet("me")]
    [Authorize]
    [ProducesResponseType(typeof(ApiResponse<UserInfoDto>), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ErrorResponse), StatusCodes.Status401Unauthorized)]
    public ActionResult<ApiResponse<UserInfoDto>> GetCurrentUser()
    {
        var userInfo = new UserInfoDto
        {
            Id = int.Parse(User.FindFirst(ClaimTypes.NameIdentifier)?.Value ?? "0"),
            FirstName = User.FindFirst("firstName")?.Value ?? string.Empty,
            LastName = User.FindFirst("lastName")?.Value ?? string.Empty,
            Email = User.FindFirst(ClaimTypes.Email)?.Value ?? string.Empty,
            Role = User.FindFirst(ClaimTypes.Role)?.Value ?? string.Empty,
            FullName = User.FindFirst(ClaimTypes.Name)?.Value ?? string.Empty
        };

        return HandleApiResponse(Application.Common.Result<UserInfoDto>.Success(userInfo));
    }

    /// <summary>
    /// Gets the client IP address from the request
    /// </summary>
    /// <returns>Client IP address</returns>
    private string? GetClientIpAddress()
    {
        return HttpContext.Connection.RemoteIpAddress?.ToString() ??
               HttpContext.Request.Headers["X-Forwarded-For"].FirstOrDefault() ??
               HttpContext.Request.Headers["X-Real-IP"].FirstOrDefault();
    }
}
