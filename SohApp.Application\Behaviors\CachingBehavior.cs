using MediatR;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Logging;
using System.Text.Json;

namespace SohApp.Application.Behaviors;

public class CachingBehavior<TRequest, TResponse> : IPipelineBehavior<TRequest, TResponse>
    where TRequest : IRequest<TResponse>
{
    private readonly IMemoryCache _cache;
    private readonly ILogger<CachingBehavior<TRequest, TResponse>> _logger;
    private readonly TimeSpan _defaultCacheDuration = TimeSpan.FromMinutes(5);

    public CachingBehavior(
        IMemoryCache cache,
        ILogger<CachingBehavior<TRequest, TResponse>> logger)
    {
        _cache = cache;
        _logger = logger;
    }

    public async Task<TResponse> Handle(TRequest request, RequestHandlerDelegate<TResponse> next, CancellationToken cancellationToken)
    {
        // Only cache queries (requests that don't modify data)
        if (!ShouldCache(request))
        {
            return await next();
        }

        var cacheKey = GenerateCacheKey(request);
        
        // Try to get from cache first
        if (_cache.TryGetValue(cacheKey, out TResponse? cachedResponse))
        {
            _logger.LogDebug("Cache hit for {RequestType} with key: {CacheKey}", 
                typeof(TRequest).Name, cacheKey);
            return cachedResponse!;
        }

        // Execute the request
        var response = await next();

        // Cache the response if it's successful
        if (ShouldCacheResponse(response))
        {
            var cacheOptions = new MemoryCacheEntryOptions
            {
                AbsoluteExpirationRelativeToNow = GetCacheDuration(request),
                Priority = CacheItemPriority.Normal
            };

            _cache.Set(cacheKey, response, cacheOptions);
            
            _logger.LogDebug("Cached response for {RequestType} with key: {CacheKey} for {Duration}", 
                typeof(TRequest).Name, cacheKey, cacheOptions.AbsoluteExpirationRelativeToNow);
        }

        return response;
    }

    private static bool ShouldCache(TRequest request)
    {
        // Only cache read operations (queries)
        var requestType = typeof(TRequest);
        var requestName = requestType.Name;
        
        // Cache queries but not commands
        return requestName.Contains("Query") || requestName.Contains("Get");
    }

    private static bool ShouldCacheResponse(TResponse response)
    {
        // Don't cache null responses or failed results
        if (response == null)
            return false;

        // If it's a Result<T>, only cache successful results
        var responseType = typeof(TResponse);
        if (responseType.IsGenericType && responseType.GetGenericTypeDefinition().Name.Contains("Result"))
        {
            var isSuccessProperty = responseType.GetProperty("IsSuccess");
            if (isSuccessProperty != null)
            {
                return (bool)isSuccessProperty.GetValue(response)!;
            }
        }

        return true;
    }

    private static string GenerateCacheKey(TRequest request)
    {
        var requestType = typeof(TRequest).Name;
        var requestJson = JsonSerializer.Serialize(request, new JsonSerializerOptions
        {
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase
        });
        
        // Create a hash of the request for a shorter cache key
        var hash = requestJson.GetHashCode();
        return $"{requestType}_{hash}";
    }

    private TimeSpan GetCacheDuration(TRequest request)
    {
        // You can implement custom cache durations based on request type
        var requestType = typeof(TRequest);
        
        return requestType.Name switch
        {
            var name when name.Contains("User") => TimeSpan.FromMinutes(10),
            var name when name.Contains("Product") => TimeSpan.FromMinutes(15),
            var name when name.Contains("Category") => TimeSpan.FromHours(1),
            var name when name.Contains("Order") => TimeSpan.FromMinutes(5),
            _ => _defaultCacheDuration
        };
    }
}
