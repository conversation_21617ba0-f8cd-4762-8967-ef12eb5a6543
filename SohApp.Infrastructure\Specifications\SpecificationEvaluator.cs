using Microsoft.EntityFrameworkCore;
using SohApp.Core.Entities.Base;
using SohApp.Core.Interfaces;

namespace SohApp.Infrastructure.Specifications;

public static class SpecificationEvaluator
{
    public static IQueryable<T> GetQuery<T>(IQueryable<T> inputQuery, ISpecification<T> specification) where T : BaseEntity
    {
        var query = inputQuery;

        // Apply no tracking if specified
        if (specification.AsNoTracking)
        {
            query = query.AsNoTracking();
        }

        // Apply criteria
        if (specification.Criteria != null)
        {
            query = query.Where(specification.Criteria);
        }

        // Apply includes - optimized to avoid unnecessary operations
        if (specification.Includes?.Count > 0)
        {
            foreach (var include in specification.Includes)
            {
                query = query.Include(include);
            }
        }

        if (specification.IncludeStrings?.Count > 0)
        {
            foreach (var include in specification.IncludeStrings)
            {
                query = query.Include(include);
            }
        }

        // Apply split query if specified
        if (specification.IsSplitQuery)
        {
            query = query.AsSplitQuery();
        }

        // Apply ordering
        if (specification.OrderBy != null)
        {
            query = query.OrderBy(specification.OrderBy);
        }
        else if (specification.OrderByDescending != null)
        {
            query = query.OrderByDescending(specification.OrderByDescending);
        }

        // Apply ThenBy expressions - only if we have an ordered query and ThenBy expressions
        if (specification.ThenByExpressions?.Count > 0 &&
            (specification.OrderBy != null || specification.OrderByDescending != null))
        {
            var orderedQuery = (IOrderedQueryable<T>)query;

            foreach (var (keySelector, descending) in specification.ThenByExpressions)
            {
                orderedQuery = descending
                    ? orderedQuery.ThenByDescending(keySelector)
                    : orderedQuery.ThenBy(keySelector);
            }

            query = orderedQuery;
        }

        // Apply paging (must be last)
        if (specification.IsPagingEnabled)
        {
            query = query.Skip(specification.Skip).Take(specification.Take);
        }

        return query;
    }

    public static IQueryable<TResult> GetQuery<T, TResult>(IQueryable<T> inputQuery, ISpecification<T, TResult> specification) where T : BaseEntity
    {
        var query = GetQuery(inputQuery, (ISpecification<T>)specification);
        
        if (specification.Selector != null)
        {
            return query.Select(specification.Selector);
        }

        throw new InvalidOperationException("Selector must be specified for projection specifications");
    }
}
