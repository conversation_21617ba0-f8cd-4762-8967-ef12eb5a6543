using Microsoft.EntityFrameworkCore;
using SohApp.Core.Entities;
using SohApp.Core.Enums;
using SohApp.Core.Interfaces.Repositories;
using SohApp.Infrastructure.Data;

namespace SohApp.Infrastructure.Repositories;

public class UserRepository : GenericRepository<User>, IUserRepository
{
    public UserRepository(ApplicationDbContext context) : base(context)
    {
    }

    public async Task<User?> GetByEmailAsync(string email, CancellationToken cancellationToken = default)
    {
        // With value converter, we can now use the Email property directly
        var emailObj = new Core.ValueObjects.Email(email);
        return await _dbSet
            .Include(u => u.Orders)
            .FirstOrDefaultAsync(u => u.Email == emailObj, cancellationToken);
    }

    public async Task<IReadOnlyList<User>> GetActiveUsersAsync(CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .Where(u => u.IsActive && !u.IsDeleted)
            .OrderBy(u => u.LastName)
            .ThenBy(u => u.FirstName)
            .AsNoTracking()
            .ToListAsync(cancellationToken);
    }

    public async Task<IReadOnlyList<User>> GetUsersByRoleAsync(UserRole role, CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .Where(u => u.Role == role && !u.IsDeleted)
            .OrderBy(u => u.CreatedAt)
            .AsNoTracking()
            .ToListAsync(cancellationToken);
    }

    public async Task<bool> IsEmailUniqueAsync(string email, int? excludeUserId = null, CancellationToken cancellationToken = default)
    {
        // With value converter, we can now use the Email property directly
        var emailObj = new Core.ValueObjects.Email(email);
        var query = _dbSet
            .AsNoTracking()
            .Where(u => u.Email == emailObj);

        if (excludeUserId.HasValue)
        {
            query = query.Where(u => u.Id != excludeUserId.Value);
        }

        return !await query.AnyAsync(cancellationToken);
    }
}
