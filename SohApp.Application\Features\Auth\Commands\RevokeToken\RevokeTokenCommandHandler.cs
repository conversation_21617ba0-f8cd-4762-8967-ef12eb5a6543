using MediatR;
using Microsoft.Extensions.Logging;
using SohApp.Application.Common;
using SohApp.Application.DTOs.Auth;
using SohApp.Application.Interfaces;

namespace SohApp.Application.Features.Auth.Commands.RevokeToken;

public class RevokeTokenCommandHandler : IRequestHandler<RevokeTokenCommand, Result>
{
    private readonly IAuthenticationService _authenticationService;
    private readonly ILogger<RevokeTokenCommandHandler> _logger;

    public RevokeTokenCommandHandler(
        IAuthenticationService authenticationService,
        ILogger<RevokeTokenCommandHandler> logger)
    {
        _authenticationService = authenticationService;
        _logger = logger;
    }

    public async Task<Result> Handle(RevokeTokenCommand request, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Processing token revocation request");

        var refreshTokenDto = new RefreshTokenDto
        {
            RefreshToken = request.RefreshToken
        };

        var result = await _authenticationService.RevokeTokenAsync(refreshTokenDto, request.IpAddress, cancellationToken);

        if (result.IsSuccess)
        {
            _logger.LogInformation("Token revocation successful");
        }
        else
        {
            _logger.LogWarning("Token revocation failed. Errors: {Errors}", string.Join(", ", result.Errors));
        }

        return result;
    }
}
