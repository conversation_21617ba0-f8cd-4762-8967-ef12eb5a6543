using Microsoft.EntityFrameworkCore;
using SohApp.Core.Entities;
using SohApp.Core.Enums;
using SohApp.Core.Specifications.Base;

namespace SohApp.Application.Features.Users.Queries.GetUsers.Specifications;

public class GetUsersSpecification : BaseSpecification<User>
{
    public GetUsersSpecification(
        UserRole? role = null,
        bool? isActive = null,
        string? searchTerm = null,
        string? sortBy = null,
        bool sortDescending = false)
    {
        // Build criteria
        if (role.HasValue)
        {
            SetCriteria(u => u.Role == role.Value && !u.IsDeleted);
        }
        else
        {
            SetCriteria(u => !u.IsDeleted);
        }

        if (isActive.HasValue)
        {
            var currentCriteria = Criteria;
            SetCriteria(u => currentCriteria!.Compile()(u) && u.IsActive == isActive.Value);
        }

        if (!string.IsNullOrWhiteSpace(searchTerm))
        {
            var searchLower = searchTerm.ToLowerInvariant();
            var currentCriteria = Criteria;

            // For search, we need to use a different approach since Email is a value object
            // We'll search in FirstName and LastName only, or use a raw SQL approach for Email
            SetCriteria(u => currentCriteria!.Compile()(u) &&
                (u.FirstName.ToLower().Contains(searchLower) ||
                 u.LastName.ToLower().Contains(searchLower)));

            // Note: Email search would require a custom specification or raw SQL
            // For now, we'll focus on name-based search to avoid EF translation issues
        }

        // Apply sorting
        switch (sortBy?.ToLowerInvariant())
        {
            case "firstname":
                if (sortDescending)
                    ApplyOrderByDescending(u => u.FirstName);
                else
                    ApplyOrderBy(u => u.FirstName);
                break;
            case "lastname":
                if (sortDescending)
                    ApplyOrderByDescending(u => u.LastName);
                else
                    ApplyOrderBy(u => u.LastName);
                break;
            case "email":
                if (sortDescending)
                    ApplyOrderByDescending(u => u.Email);
                else
                    ApplyOrderBy(u => u.Email);
                break;
            case "createdat":
                if (sortDescending)
                    ApplyOrderByDescending(u => u.CreatedAt);
                else
                    ApplyOrderBy(u => u.CreatedAt);
                break;
            default:
                ApplyOrderBy(u => u.LastName);
                ApplyThenBy(u => u.FirstName);
                break;
        }

        EnableNoTracking();
    }
}
