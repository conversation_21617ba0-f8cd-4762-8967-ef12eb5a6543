using SohApp.Core.Events.Base;

namespace SohApp.Core.Events.ProductEvents;

public class ProductBackInStockEvent : DomainEvent
{
    public int ProductId { get; }
    public string ProductName { get; }
    public int NewStockQuantity { get; }

    public ProductBackInStockEvent(int productId, string productName, int newStockQuantity)
    {
        ProductId = productId;
        ProductName = productName;
        NewStockQuantity = newStockQuantity;
    }
}
