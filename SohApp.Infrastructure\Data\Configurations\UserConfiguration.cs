using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using SohApp.Core.Constants;
using SohApp.Core.Entities;
using SohApp.Infrastructure.Data.Converters;

namespace SohApp.Infrastructure.Data.Configurations;

public class UserConfiguration : IEntityTypeConfiguration<User>
{
    public void Configure(EntityTypeBuilder<User> builder)
    {
        builder.ToTable("Users");
        
        builder.<PERSON><PERSON>ey(u => u.Id);
        
        builder.Property(u => u.FirstName)
            .IsRequired()
            .HasMaxLength(DomainConstants.User.FirstNameMaxLength);
            
        builder.Property(u => u.LastName)
            .IsRequired()
            .HasMaxLength(DomainConstants.User.LastNameMaxLength);
            
        // Configure Email value object with value converter for better EF Core support
        builder.Property(u => u.Email)
            .HasConversion<EmailConverter>()
            .HasColumnName("Email")
            .IsRequired()
            .HasMaxLength(DomainConstants.User.EmailMaxLength);

        // Create unique index on Email
        builder.HasIndex(u => u.Email)
            .IsUnique()
            .HasDatabaseName("IX_Users_Email");

        // Performance indexes
        builder.HasIndex(u => u.IsActive)
            .HasDatabaseName("IX_Users_IsActive");

        builder.HasIndex(u => u.Role)
            .HasDatabaseName("IX_Users_Role");

        builder.HasIndex(u => u.CreatedAt)
            .HasDatabaseName("IX_Users_CreatedAt");

        builder.HasIndex(u => new { u.IsActive, u.Role })
            .HasDatabaseName("IX_Users_IsActive_Role");
            
        builder.Property(u => u.PasswordHash)
            .IsRequired()
            .HasMaxLength(500);
            
        builder.Property(u => u.Role)
            .IsRequired()
            .HasConversion<string>();
            
        builder.Property(u => u.IsActive)
            .IsRequired()
            .HasDefaultValue(true);
            
        builder.Property(u => u.CreatedAt)
            .IsRequired();
            
        builder.Property(u => u.CreatedBy)
            .IsRequired()
            .HasMaxLength(100);
            
        // Navigation properties
        builder.HasMany(u => u.Orders)
            .WithOne(o => o.User)
            .HasForeignKey(o => o.UserId)
            .OnDelete(DeleteBehavior.Restrict);

        // Ignore computed property
        builder.Ignore(u => u.FullName);
    }
}
