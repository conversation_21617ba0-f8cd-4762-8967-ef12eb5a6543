using MediatR;
using SohApp.Application.Common;
using SohApp.Application.DTOs.User;
using SohApp.Core.Enums;

namespace SohApp.Application.Features.Users.Queries.GetUsers;

public class GetUsersQuery : IRequest<Result<PagedResult<UserDto>>>
{
    public int Page { get; set; } = 1;
    public int PageSize { get; set; } = 10;
    public UserRole? Role { get; set; }
    public bool? IsActive { get; set; }
    public string? SearchTerm { get; set; }
    public string? SortBy { get; set; }
    public bool SortDescending { get; set; }
}
