using SohApp.Core.Events.Base;

namespace SohApp.Core.Events.OrderEvents;

public class OrderDeliveredEvent : DomainEvent
{
    public int OrderId { get; }
    public int UserId { get; }
    public DateTime DeliveredAt { get; }

    public OrderDeliveredEvent(int orderId, int userId, DateTime deliveredAt)
    {
        OrderId = orderId;
        UserId = userId;
        DeliveredAt = deliveredAt;
    }
}
