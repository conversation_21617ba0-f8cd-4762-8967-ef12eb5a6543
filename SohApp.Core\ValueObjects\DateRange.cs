namespace SohApp.Core.ValueObjects;

public class DateRange : IEquatable<DateRange>
{
    public DateTime StartDate { get; }
    public DateTime EndDate { get; }
    
    public TimeSpan Duration => EndDate - StartDate;
    public int DaysCount => (int)Math.Ceiling(Duration.TotalDays);

    public DateRange(DateTime startDate, DateTime endDate)
    {
        if (startDate > endDate)
            throw new ArgumentException("Start date cannot be after end date", nameof(startDate));

        StartDate = startDate.Date; // Normalize to date only
        EndDate = endDate.Date;
    }

    public DateRange(DateTime startDate, TimeSpan duration)
    {
        if (duration < TimeSpan.Zero)
            throw new ArgumentException("Duration cannot be negative", nameof(duration));

        StartDate = startDate.Date;
        EndDate = startDate.Date.Add(duration);
    }

    public static DateRange FromDays(DateTime startDate, int days)
    {
        if (days < 0)
            throw new ArgumentException("Days cannot be negative", nameof(days));

        return new DateRange(startDate, startDate.AddDays(days));
    }

    public static DateRange ThisWeek()
    {
        var today = DateTime.Today;
        var startOfWeek = today.AddDays(-(int)today.DayOfWeek);
        var endOfWeek = startOfWeek.AddDays(6);
        return new DateRange(startOfWeek, endOfWeek);
    }

    public static DateRange ThisMonth()
    {
        var today = DateTime.Today;
        var startOfMonth = new DateTime(today.Year, today.Month, 1);
        var endOfMonth = startOfMonth.AddMonths(1).AddDays(-1);
        return new DateRange(startOfMonth, endOfMonth);
    }

    public static DateRange ThisYear()
    {
        var today = DateTime.Today;
        var startOfYear = new DateTime(today.Year, 1, 1);
        var endOfYear = new DateTime(today.Year, 12, 31);
        return new DateRange(startOfYear, endOfYear);
    }

    public bool Contains(DateTime date)
    {
        var dateOnly = date.Date;
        return dateOnly >= StartDate && dateOnly <= EndDate;
    }

    public bool Overlaps(DateRange other)
    {
        return StartDate <= other.EndDate && EndDate >= other.StartDate;
    }

    public DateRange? GetOverlap(DateRange other)
    {
        if (!Overlaps(other))
            return null;

        var overlapStart = StartDate > other.StartDate ? StartDate : other.StartDate;
        var overlapEnd = EndDate < other.EndDate ? EndDate : other.EndDate;

        return new DateRange(overlapStart, overlapEnd);
    }

    public IEnumerable<DateTime> GetDatesInRange()
    {
        var current = StartDate;
        while (current <= EndDate)
        {
            yield return current;
            current = current.AddDays(1);
        }
    }

    public bool Equals(DateRange? other) =>
        other is not null && StartDate == other.StartDate && EndDate == other.EndDate;

    public override bool Equals(object? obj) => Equals(obj as DateRange);

    public override int GetHashCode() => HashCode.Combine(StartDate, EndDate);

    public override string ToString() => $"{StartDate:yyyy-MM-dd} to {EndDate:yyyy-MM-dd}";

    public string ToString(string format) => $"{StartDate.ToString(format)} to {EndDate.ToString(format)}";
}
