using System.Text.RegularExpressions;

namespace SohApp.Core.ValueObjects;

public class PhoneNumber : IEquatable<PhoneNumber>
{
    private static readonly Regex PhoneRegex = new(@"^\+?[1-9]\d{1,14}$", RegexOptions.Compiled);
    
    public string Value { get; }
    public string CountryCode { get; }
    public string Number { get; }

    public PhoneNumber(string value)
    {
        if (string.IsNullOrWhiteSpace(value))
            throw new ArgumentException("Phone number cannot be empty", nameof(value));

        var cleanValue = CleanPhoneNumber(value);
        
        if (!IsValidPhoneNumber(cleanValue))
            throw new ArgumentException("Invalid phone number format", nameof(value));

        Value = cleanValue;
        (CountryCode, Number) = ParsePhoneNumber(cleanValue);
    }

    public PhoneNumber(string countryCode, string number)
    {
        if (string.IsNullOrWhiteSpace(countryCode))
            throw new ArgumentException("Country code cannot be empty", nameof(countryCode));
        
        if (string.IsNullOrWhiteSpace(number))
            throw new ArgumentException("Phone number cannot be empty", nameof(number));

        var fullNumber = $"{countryCode}{number}";
        
        if (!IsValidPhoneNumber(fullNumber))
            throw new ArgumentException("Invalid phone number format");

        Value = fullNumber;
        CountryCode = countryCode;
        Number = number;
    }

    private static string CleanPhoneNumber(string phoneNumber)
    {
        // Remove all non-digit characters except +
        var cleaned = Regex.Replace(phoneNumber, @"[^\d+]", "");
        
        // Ensure it starts with + if it doesn't already
        if (!cleaned.StartsWith("+") && cleaned.Length > 0)
        {
            cleaned = "+" + cleaned;
        }
        
        return cleaned;
    }

    private static bool IsValidPhoneNumber(string phoneNumber)
    {
        return PhoneRegex.IsMatch(phoneNumber);
    }

    private static (string CountryCode, string Number) ParsePhoneNumber(string phoneNumber)
    {
        if (phoneNumber.StartsWith("+"))
        {
            // Simple parsing - first 1-4 digits after + are country code
            var digits = phoneNumber.Substring(1);
            
            // Common country code lengths
            for (int i = 1; i <= Math.Min(4, digits.Length - 1); i++)
            {
                var countryCode = "+" + digits.Substring(0, i);
                var number = digits.Substring(i);
                
                if (number.Length >= 4) // Minimum number length
                {
                    return (countryCode, number);
                }
            }
        }
        
        return ("+1", phoneNumber.Substring(1)); // Default to +1 if parsing fails
    }

    public string GetFormattedNumber()
    {
        if (CountryCode == "+1" && Number.Length == 10)
        {
            // US format: (XXX) XXX-XXXX
            return $"({Number.Substring(0, 3)}) {Number.Substring(3, 3)}-{Number.Substring(6)}";
        }
        
        return $"{CountryCode} {Number}";
    }

    public bool Equals(PhoneNumber? other) => 
        other is not null && Value == other.Value;

    public override bool Equals(object? obj) => Equals(obj as PhoneNumber);
    
    public override int GetHashCode() => Value.GetHashCode();
    
    public override string ToString() => GetFormattedNumber();

    public static implicit operator string(PhoneNumber phoneNumber) => phoneNumber.Value;
    public static explicit operator PhoneNumber(string phoneNumber) => new(phoneNumber);
}
