using SohApp.Core.Enums;
using SohApp.Core.Events.Base;

namespace SohApp.Core.Events.UserEvents;

public class UserRoleChangedEvent : DomainEvent
{
    public int UserId { get; }
    public string Email { get; }
    public UserRole OldRole { get; }
    public UserRole NewRole { get; }

    public UserRoleChangedEvent(int userId, string email, UserRole oldRole, UserRole newRole)
    {
        UserId = userId;
        Email = email;
        OldRole = oldRole;
        NewRole = newRole;
    }
}
