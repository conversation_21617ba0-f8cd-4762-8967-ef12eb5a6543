using Microsoft.EntityFrameworkCore;
using SohApp.Core.Constants;
using SohApp.Core.Entities;
using SohApp.Core.Enums;
using SohApp.Core.Interfaces.Repositories;
using SohApp.Infrastructure.Data;

namespace SohApp.Infrastructure.Repositories;

public class OrderRepository : GenericRepository<Order>, IOrderRepository
{
    public OrderRepository(ApplicationDbContext context) : base(context)
    {
    }

    public async Task<IReadOnlyList<Order>> GetByUserIdAsync(int userId, CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .Include(o => o.OrderItems)
                .ThenInclude(oi => oi.Product)
            .Where(o => o.UserId == userId)
            .OrderByDescending(o => o.CreatedAt)
            .AsNoTracking()
            .ToListAsync(cancellationToken);
    }

    public async Task<IReadOnlyList<Order>> GetByDateRangeAsync(DateTime startDate, DateTime endDate, CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .Include(o => o.User)
            .Include(o => o.OrderItems)
                .ThenInclude(oi => oi.Product)
            .Where(o => o.CreatedAt >= startDate && o.CreatedAt <= endDate)
            .OrderByDescending(o => o.CreatedAt)
            .AsNoTracking()
            .ToListAsync(cancellationToken);
    }

    public async Task<IReadOnlyList<Order>> GetPendingOrdersAsync(CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .Include(o => o.User)
            .Include(o => o.OrderItems)
                .ThenInclude(oi => oi.Product)
            .Where(o => o.Status == OrderStatus.Pending)
            .OrderBy(o => o.CreatedAt)
            .AsNoTracking()
            .ToListAsync(cancellationToken);
    }

    public async Task<string> GenerateOrderNumberAsync(CancellationToken cancellationToken = default)
    {
        // Generate order number format: ORD + YYMMDD + NNN = 12 characters total
        // ORD (3) + YYMMDD (6) + NNN (3) = 12 characters
        var today = DateTime.UtcNow.ToString("yyMMdd"); // Use 2-digit year to fit 12 characters
        var prefix = $"{DomainConstants.Order.OrderNumberPrefix}{today}"; // ORD + YYMMDD = 9 characters

        var lastOrder = await _dbSet
            .Where(o => o.OrderNumber.StartsWith(prefix))
            .OrderByDescending(o => o.OrderNumber)
            .FirstOrDefaultAsync(cancellationToken);

        if (lastOrder == null)
        {
            return $"{prefix}001"; // 9 + 3 = 12 characters
        }

        var lastSequence = lastOrder.OrderNumber.Substring(prefix.Length);
        if (int.TryParse(lastSequence, out var sequence))
        {
            var nextSequence = sequence + 1;
            if (nextSequence > 999)
            {
                throw new InvalidOperationException($"Maximum order sequence reached for date {today}. Cannot generate more than 999 orders per day.");
            }
            return $"{prefix}{nextSequence:D3}"; // 9 + 3 = 12 characters
        }

        return $"{prefix}001"; // 9 + 3 = 12 characters
    }
}
