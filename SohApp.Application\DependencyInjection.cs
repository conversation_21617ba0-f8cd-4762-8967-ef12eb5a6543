using System.Reflection;
using FluentValidation;
using MediatR;
using Microsoft.Extensions.DependencyInjection;
using SohApp.Application.Behaviors;
using SohApp.Application.EventHandlers.OrderEventHandlers;
using SohApp.Application.EventHandlers.UserEventHandlers;
using SohApp.Application.Interfaces;
using SohApp.Core.Events.OrderEvents;
using SohApp.Core.Events.UserEvents;
using SohApp.Core.Interfaces;

namespace SohApp.Application;

public static class DependencyInjection
{
    public static IServiceCollection AddApplication(this IServiceCollection services)
    {
        var assembly = Assembly.GetExecutingAssembly();

        // MediatR
        services.AddMediatR(cfg => cfg.RegisterServicesFromAssembly(assembly));

        // AutoMapper
        services.AddAutoMapper(cfg => cfg.AddMaps(assembly));

        // FluentValidation
        services.AddValidatorsFromAssembly(assembly);

        // Pipeline Behaviors (order matters - validation first, then caching, then logging, then performance)
        services.AddTransient(typeof(IPipelineBehavior<,>), typeof(ValidationBehavior<,>));
        services.AddTransient(typeof(IPipelineBehavior<,>), typeof(CachingBehavior<,>));
        services.AddTransient(typeof(IPipelineBehavior<,>), typeof(LoggingBehavior<,>));
        services.AddTransient(typeof(IPipelineBehavior<,>), typeof(PerformanceBehavior<,>));

        // Domain Event Handlers
        services.AddScoped<IDomainEventHandler<UserCreatedEvent>, UserCreatedEventHandler>();
        services.AddScoped<IDomainEventHandler<OrderCreatedEvent>, OrderCreatedEventHandler>();

        // Application Services
        services.AddScoped<IPasswordHasher, PasswordHasher>();

        return services;
    }
}

// Simple password hasher implementation
public class PasswordHasher : IPasswordHasher
{
    public string HashPassword(string password)
    {
        // In a real application, use BCrypt, Argon2, or similar
        // This is just for demonstration
        return BCrypt.Net.BCrypt.HashPassword(password);
    }

    public bool VerifyPassword(string hashedPassword, string providedPassword)
    {
        return BCrypt.Net.BCrypt.Verify(providedPassword, hashedPassword);
    }
}
