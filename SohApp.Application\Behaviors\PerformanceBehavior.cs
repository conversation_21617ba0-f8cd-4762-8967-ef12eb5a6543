using System.Diagnostics;
using MediatR;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using SohApp.Core.Interfaces.Services;

namespace SohApp.Application.Behaviors;

public class PerformanceBehavior<TRequest, TResponse> : IPipelineBehavior<TRequest, TResponse>
    where TRequest : IRequest<TResponse>
{
    private readonly ILogger<PerformanceBehavior<TRequest, TResponse>> _logger;
    private readonly ICurrentUserService? _currentUserService;
    private readonly int _longRunningRequestThreshold;
    private readonly bool _trackMemoryUsage;

    public PerformanceBehavior(
        ILogger<PerformanceBehavior<TRequest, TResponse>> logger,
        IConfiguration configuration,
        ICurrentUserService? currentUserService = null)
    {
        _logger = logger;
        _currentUserService = currentUserService;

        // Make threshold configurable with default fallback
        _longRunningRequestThreshold = configuration.GetValue<int>("Performance:LongRunningRequestThreshold", 500);
        _trackMemoryUsage = configuration.GetValue<bool>("Performance:TrackMemoryUsage", false);
    }

    public async Task<TResponse> Handle(TRequest request, RequestHandlerDelegate<TResponse> next, CancellationToken cancellationToken)
    {
        var requestName = typeof(TRequest).Name;
        var stopwatch = Stopwatch.StartNew();

        // Track memory usage if enabled
        var initialMemory = _trackMemoryUsage ? GC.GetTotalMemory(false) : 0;

        try
        {
            var response = await next();
            stopwatch.Stop();

            LogPerformanceMetrics(requestName, stopwatch.ElapsedMilliseconds, initialMemory, null);

            return response;
        }
        catch (Exception ex)
        {
            stopwatch.Stop();
            LogPerformanceMetrics(requestName, stopwatch.ElapsedMilliseconds, initialMemory, ex);
            throw;
        }
    }

    private void LogPerformanceMetrics(string requestName, long elapsedMilliseconds, long initialMemory, Exception? exception)
    {
        var userId = GetCurrentUserId();

        // Always log performance metrics for analysis
        using var scope = _logger.BeginScope(new Dictionary<string, object>
        {
            ["RequestName"] = requestName,
            ["UserId"] = userId,
            ["ElapsedMilliseconds"] = elapsedMilliseconds,
            ["HasException"] = exception != null
        });

        if (_trackMemoryUsage)
        {
            var finalMemory = GC.GetTotalMemory(false);
            var memoryUsed = finalMemory - initialMemory;

            _logger.LogInformation("Request {RequestName} completed in {ElapsedMilliseconds}ms, Memory used: {MemoryUsed} bytes",
                requestName, elapsedMilliseconds, memoryUsed);
        }

        // Log warning for long-running requests
        if (elapsedMilliseconds > _longRunningRequestThreshold)
        {
            if (exception != null)
            {
                _logger.LogWarning("Long running request with exception: {RequestName} for user {UserId} took {ElapsedMilliseconds}ms - {ExceptionType}: {ExceptionMessage}",
                    requestName, userId, elapsedMilliseconds, exception.GetType().Name, exception.Message);
            }
            else
            {
                _logger.LogWarning("Long running request detected: {RequestName} for user {UserId} took {ElapsedMilliseconds}ms",
                    requestName, userId, elapsedMilliseconds);
            }
        }
        else if (exception == null)
        {
            // Log successful requests at debug level
            _logger.LogDebug("Request {RequestName} completed successfully in {ElapsedMilliseconds}ms",
                requestName, elapsedMilliseconds);
        }
    }

    private string GetCurrentUserId()
    {
        try
        {
            return _currentUserService?.UserId ?? "Anonymous";
        }
        catch (Exception ex)
        {
            _logger.LogDebug(ex, "Failed to get current user ID");
            return "Unknown";
        }
    }
}
