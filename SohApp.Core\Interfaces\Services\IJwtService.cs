using SohApp.Core.Entities;
using System.Security.Claims;

namespace SohApp.Core.Interfaces.Services;

public interface IJwtService
{
    /// <summary>
    /// Generates a JWT access token for the user
    /// </summary>
    /// <param name="user">User to generate token for</param>
    /// <returns>JWT token string</returns>
    string GenerateAccessToken(User user);

    /// <summary>
    /// Generates a refresh token
    /// </summary>
    /// <returns>Refresh token string</returns>
    string GenerateRefreshToken();

    /// <summary>
    /// Gets the principal from an expired token (for refresh token validation)
    /// </summary>
    /// <param name="token">JWT token</param>
    /// <returns>Claims principal or null if invalid</returns>
    ClaimsPrincipal? GetPrincipalFromExpiredToken(string token);

    /// <summary>
    /// Gets the access token expiry time
    /// </summary>
    /// <returns>DateTime when access token expires</returns>
    DateTime GetAccessTokenExpiryTime();

    /// <summary>
    /// Gets the refresh token expiry time
    /// </summary>
    /// <returns>DateTime when refresh token expires</returns>
    DateTime GetRefreshTokenExpiryTime();
}
