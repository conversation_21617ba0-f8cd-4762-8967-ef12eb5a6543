using FluentValidation;
using SohApp.Core.Constants;

namespace SohApp.Application.Features.Users.Commands.CreateUser;

public class CreateUserCommandValidator : AbstractValidator<CreateUserCommand>
{
    public CreateUserCommandValidator()
    {
        RuleFor(x => x.FirstName)
            .NotEmpty().WithMessage("First name is required")
            .MaximumLength(DomainConstants.User.FirstNameMaxLength).WithMessage($"First name must not exceed {DomainConstants.User.FirstNameMaxLength} characters");

        RuleFor(x => x.LastName)
            .NotEmpty().WithMessage("Last name is required")
            .MaximumLength(DomainConstants.User.LastNameMaxLength).WithMessage($"Last name must not exceed {DomainConstants.User.LastNameMaxLength} characters");

        RuleFor(x => x.Email)
            .NotEmpty().WithMessage("Email is required")
            .EmailAddress().WithMessage("Invalid email format")
            .MaximumLength(DomainConstants.User.EmailMaxLength).WithMessage($"Email must not exceed {DomainConstants.User.EmailMaxLength} characters");

        RuleFor(x => x.Password)
            .NotEmpty().WithMessage("Password is required")
            .MinimumLength(DomainConstants.User.PasswordMinLength).WithMessage($"Password must be at least {DomainConstants.User.PasswordMinLength} characters")
            .Matches(@"^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]")
            .WithMessage("Password must contain at least one uppercase letter, one lowercase letter, one digit, and one special character");

        RuleFor(x => x.Role)
            .IsInEnum().WithMessage("Invalid user role");
    }
}
