{"openapi": "3.0.4", "info": {"title": "SohApp API", "description": "A Clean Architecture API built with .NET Core", "contact": {"name": "SohApp Team", "email": "<EMAIL>"}, "version": "v1"}, "paths": {"/api/Auth/login": {"post": {"tags": ["<PERSON><PERSON>"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/LoginDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/LoginDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/LoginDto"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/LoginResponseDtoApiResponse"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "422": {"description": "Unprocessable Content", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "429": {"description": "Too Many Requests", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/api/Auth/refresh": {"post": {"tags": ["<PERSON><PERSON>"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/RefreshTokenDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/RefreshTokenDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/RefreshTokenDto"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RefreshTokenResponseDtoApiResponse"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "422": {"description": "Unprocessable Content", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "429": {"description": "Too Many Requests", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/api/Auth/revoke": {"post": {"tags": ["<PERSON><PERSON>"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/RefreshTokenDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/RefreshTokenDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/RefreshTokenDto"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "422": {"description": "Unprocessable Content", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "429": {"description": "Too Many Requests", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/api/Auth/revoke-all": {"post": {"tags": ["<PERSON><PERSON>"], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "429": {"description": "Too Many Requests", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/api/Auth/me": {"get": {"tags": ["<PERSON><PERSON>"], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserInfoDtoApiResponse"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/api/Orders": {"get": {"tags": ["Orders"], "parameters": [{"name": "Page", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "PageSize", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "UserId", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "Status", "in": "query", "schema": {"$ref": "#/components/schemas/OrderStatus"}}, {"name": "StartDate", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "EndDate", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "SearchTerm", "in": "query", "schema": {"type": "string"}}, {"name": "SortBy", "in": "query", "schema": {"type": "string"}}, {"name": "SortDescending", "in": "query", "schema": {"type": "boolean"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/OrderDtoPagedResultApiResponse"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}, "post": {"tags": ["Orders"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateOrderDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateOrderDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateOrderDto"}}}}, "responses": {"201": {"description": "Created", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Int32ApiResponse"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/api/Orders/{id}": {"get": {"tags": ["Orders"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/OrderDetailDtoApiResponse"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/api/Orders/user/{userId}": {"get": {"tags": ["Orders"], "parameters": [{"name": "userId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}, {"name": "page", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 1}}, {"name": "pageSize", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 10}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/OrderDtoPagedResultApiResponse"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/api/Orders/pending": {"get": {"tags": ["Orders"], "parameters": [{"name": "page", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 1}}, {"name": "pageSize", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 10}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/OrderDtoPagedResultApiResponse"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/api/Products": {"get": {"tags": ["Products"], "parameters": [{"name": "Page", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "PageSize", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "CategoryId", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "SearchTerm", "in": "query", "schema": {"type": "string"}}, {"name": "MinPrice", "in": "query", "schema": {"type": "number", "format": "double"}}, {"name": "MaxPrice", "in": "query", "schema": {"type": "number", "format": "double"}}, {"name": "InStock", "in": "query", "schema": {"type": "boolean"}}, {"name": "SortBy", "in": "query", "schema": {"type": "string"}}, {"name": "SortDescending", "in": "query", "schema": {"type": "boolean"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProductDtoPagedResultApiResponse"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}, "post": {"tags": ["Products"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateProductDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateProductDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateProductDto"}}}}, "responses": {"201": {"description": "Created", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Int32ApiResponse"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/api/Products/{id}": {"get": {"tags": ["Products"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProductDtoApiResponse"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/api/Users": {"get": {"tags": ["Users"], "parameters": [{"name": "Page", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "PageSize", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "Role", "in": "query", "schema": {"$ref": "#/components/schemas/UserRole"}}, {"name": "IsActive", "in": "query", "schema": {"type": "boolean"}}, {"name": "SearchTerm", "in": "query", "schema": {"type": "string"}}, {"name": "SortBy", "in": "query", "schema": {"type": "string"}}, {"name": "SortDescending", "in": "query", "schema": {"type": "boolean"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserDtoPagedResultApiResponse"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}, "post": {"tags": ["Users"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateUserDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateUserDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateUserDto"}}}, "required": true}, "responses": {"201": {"description": "Created", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Int32ApiResponse"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "409": {"description": "Conflict", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "422": {"description": "Unprocessable Content", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/api/Users/<USER>": {"get": {"tags": ["Users"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"maximum": 2147483647, "minimum": 1, "type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserDtoApiResponse"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}, "put": {"tags": ["Users"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"maximum": 2147483647, "minimum": 1, "type": "integer", "format": "int32"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateUserDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdateUserDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdateUserDto"}}}, "required": true}, "responses": {"204": {"description": "No Content"}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "422": {"description": "Unprocessable Content", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}, "delete": {"tags": ["Users"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"maximum": 2147483647, "minimum": 1, "type": "integer", "format": "int32"}}], "responses": {"204": {"description": "No Content"}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "403": {"description": "Forbidden", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}}, "components": {"schemas": {"ApiResponse": {"type": "object", "properties": {"success": {"type": "boolean"}, "data": {"nullable": true}, "message": {"type": "string", "nullable": true}, "errors": {"type": "array", "items": {"type": "string"}, "nullable": true}}, "additionalProperties": false}, "CreateOrderDto": {"type": "object", "properties": {"userId": {"type": "integer", "format": "int32"}, "items": {"type": "array", "items": {"$ref": "#/components/schemas/CreateOrderItemDto"}, "nullable": true}}, "additionalProperties": false}, "CreateOrderItemDto": {"type": "object", "properties": {"productId": {"type": "integer", "format": "int32"}, "quantity": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "CreateProductDto": {"type": "object", "properties": {"name": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "sku": {"type": "string", "nullable": true}, "price": {"type": "number", "format": "double"}, "currency": {"type": "string", "nullable": true}, "stockQuantity": {"type": "integer", "format": "int32"}, "categoryId": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "CreateUserDto": {"type": "object", "properties": {"firstName": {"type": "string", "nullable": true}, "lastName": {"type": "string", "nullable": true}, "email": {"type": "string", "nullable": true}, "password": {"type": "string", "nullable": true}, "role": {"$ref": "#/components/schemas/UserRole"}}, "additionalProperties": false}, "ErrorResponse": {"type": "object", "properties": {"message": {"type": "string", "nullable": true}, "errors": {"type": "array", "items": {"type": "string"}, "nullable": true}, "traceId": {"type": "string", "nullable": true}, "timestamp": {"type": "string", "format": "date-time"}}, "additionalProperties": false}, "Int32ApiResponse": {"type": "object", "properties": {"success": {"type": "boolean"}, "data": {"type": "integer", "format": "int32"}, "message": {"type": "string", "nullable": true}, "errors": {"type": "array", "items": {"type": "string"}, "nullable": true}}, "additionalProperties": false}, "LoginDto": {"required": ["email", "password"], "type": "object", "properties": {"email": {"minLength": 1, "type": "string", "format": "email"}, "password": {"minLength": 6, "type": "string"}}, "additionalProperties": false}, "LoginResponseDto": {"type": "object", "properties": {"accessToken": {"type": "string", "nullable": true}, "refreshToken": {"type": "string", "nullable": true}, "accessTokenExpiresAt": {"type": "string", "format": "date-time"}, "refreshTokenExpiresAt": {"type": "string", "format": "date-time"}, "user": {"$ref": "#/components/schemas/UserInfoDto"}}, "additionalProperties": false}, "LoginResponseDtoApiResponse": {"type": "object", "properties": {"success": {"type": "boolean"}, "data": {"$ref": "#/components/schemas/LoginResponseDto"}, "message": {"type": "string", "nullable": true}, "errors": {"type": "array", "items": {"type": "string"}, "nullable": true}}, "additionalProperties": false}, "OrderDetailDto": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "orderNumber": {"type": "string", "nullable": true}, "status": {"type": "string", "nullable": true}, "totalAmount": {"type": "number", "format": "double"}, "currency": {"type": "string", "nullable": true}, "userId": {"type": "integer", "format": "int32"}, "userName": {"type": "string", "nullable": true}, "userEmail": {"type": "string", "nullable": true}, "createdAt": {"type": "string", "format": "date-time"}, "shippedAt": {"type": "string", "format": "date-time", "nullable": true}, "deliveredAt": {"type": "string", "format": "date-time", "nullable": true}, "items": {"type": "array", "items": {"$ref": "#/components/schemas/OrderItemDto"}, "nullable": true}}, "additionalProperties": false}, "OrderDetailDtoApiResponse": {"type": "object", "properties": {"success": {"type": "boolean"}, "data": {"$ref": "#/components/schemas/OrderDetailDto"}, "message": {"type": "string", "nullable": true}, "errors": {"type": "array", "items": {"type": "string"}, "nullable": true}}, "additionalProperties": false}, "OrderDto": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "orderNumber": {"type": "string", "nullable": true}, "status": {"type": "string", "nullable": true}, "totalAmount": {"type": "number", "format": "double"}, "currency": {"type": "string", "nullable": true}, "userName": {"type": "string", "nullable": true}, "itemCount": {"type": "integer", "format": "int32"}, "createdAt": {"type": "string", "format": "date-time"}, "shippedAt": {"type": "string", "format": "date-time", "nullable": true}, "deliveredAt": {"type": "string", "format": "date-time", "nullable": true}}, "additionalProperties": false}, "OrderDtoPagedResult": {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/components/schemas/OrderDto"}, "nullable": true}, "totalCount": {"type": "integer", "format": "int32"}, "page": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "totalPages": {"type": "integer", "format": "int32"}, "hasPreviousPage": {"type": "boolean"}, "hasNextPage": {"type": "boolean"}, "previousPage": {"type": "integer", "format": "int32", "nullable": true, "readOnly": true}, "nextPage": {"type": "integer", "format": "int32", "nullable": true, "readOnly": true}, "startIndex": {"type": "integer", "format": "int32", "readOnly": true}, "endIndex": {"type": "integer", "format": "int32", "readOnly": true}, "isEmpty": {"type": "boolean", "readOnly": true}, "isFirstPage": {"type": "boolean", "readOnly": true}, "isLastPage": {"type": "boolean", "readOnly": true}}, "additionalProperties": false}, "OrderDtoPagedResultApiResponse": {"type": "object", "properties": {"success": {"type": "boolean"}, "data": {"$ref": "#/components/schemas/OrderDtoPagedResult"}, "message": {"type": "string", "nullable": true}, "errors": {"type": "array", "items": {"type": "string"}, "nullable": true}}, "additionalProperties": false}, "OrderItemDto": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "productName": {"type": "string", "nullable": true}, "quantity": {"type": "integer", "format": "int32"}, "unitPrice": {"type": "number", "format": "double"}, "totalPrice": {"type": "number", "format": "double"}}, "additionalProperties": false}, "OrderStatus": {"enum": [1, 2, 3, 4, 5, 6, 7], "type": "integer", "format": "int32"}, "ProductDto": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "name": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "sku": {"type": "string", "nullable": true}, "price": {"type": "number", "format": "double"}, "currency": {"type": "string", "nullable": true}, "stockQuantity": {"type": "integer", "format": "int32"}, "categoryId": {"type": "integer", "format": "int32"}, "categoryName": {"type": "string", "nullable": true}, "isActive": {"type": "boolean"}, "createdAt": {"type": "string", "format": "date-time"}}, "additionalProperties": false}, "ProductDtoApiResponse": {"type": "object", "properties": {"success": {"type": "boolean"}, "data": {"$ref": "#/components/schemas/ProductDto"}, "message": {"type": "string", "nullable": true}, "errors": {"type": "array", "items": {"type": "string"}, "nullable": true}}, "additionalProperties": false}, "ProductDtoPagedResult": {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/components/schemas/ProductDto"}, "nullable": true}, "totalCount": {"type": "integer", "format": "int32"}, "page": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "totalPages": {"type": "integer", "format": "int32"}, "hasPreviousPage": {"type": "boolean"}, "hasNextPage": {"type": "boolean"}, "previousPage": {"type": "integer", "format": "int32", "nullable": true, "readOnly": true}, "nextPage": {"type": "integer", "format": "int32", "nullable": true, "readOnly": true}, "startIndex": {"type": "integer", "format": "int32", "readOnly": true}, "endIndex": {"type": "integer", "format": "int32", "readOnly": true}, "isEmpty": {"type": "boolean", "readOnly": true}, "isFirstPage": {"type": "boolean", "readOnly": true}, "isLastPage": {"type": "boolean", "readOnly": true}}, "additionalProperties": false}, "ProductDtoPagedResultApiResponse": {"type": "object", "properties": {"success": {"type": "boolean"}, "data": {"$ref": "#/components/schemas/ProductDtoPagedResult"}, "message": {"type": "string", "nullable": true}, "errors": {"type": "array", "items": {"type": "string"}, "nullable": true}}, "additionalProperties": false}, "RefreshTokenDto": {"required": ["refreshToken"], "type": "object", "properties": {"refreshToken": {"minLength": 1, "type": "string"}}, "additionalProperties": false}, "RefreshTokenResponseDto": {"type": "object", "properties": {"accessToken": {"type": "string", "nullable": true}, "refreshToken": {"type": "string", "nullable": true}, "accessTokenExpiresAt": {"type": "string", "format": "date-time"}, "refreshTokenExpiresAt": {"type": "string", "format": "date-time"}}, "additionalProperties": false}, "RefreshTokenResponseDtoApiResponse": {"type": "object", "properties": {"success": {"type": "boolean"}, "data": {"$ref": "#/components/schemas/RefreshTokenResponseDto"}, "message": {"type": "string", "nullable": true}, "errors": {"type": "array", "items": {"type": "string"}, "nullable": true}}, "additionalProperties": false}, "UpdateUserDto": {"type": "object", "properties": {"firstName": {"type": "string", "nullable": true}, "lastName": {"type": "string", "nullable": true}, "role": {"$ref": "#/components/schemas/UserRole"}, "isActive": {"type": "boolean"}}, "additionalProperties": false}, "UserDto": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "firstName": {"type": "string", "nullable": true}, "lastName": {"type": "string", "nullable": true}, "fullName": {"type": "string", "nullable": true}, "email": {"type": "string", "nullable": true}, "role": {"$ref": "#/components/schemas/UserRole"}, "isActive": {"type": "boolean"}, "createdAt": {"type": "string", "format": "date-time"}, "lastLoginAt": {"type": "string", "format": "date-time", "nullable": true}}, "additionalProperties": false}, "UserDtoApiResponse": {"type": "object", "properties": {"success": {"type": "boolean"}, "data": {"$ref": "#/components/schemas/UserDto"}, "message": {"type": "string", "nullable": true}, "errors": {"type": "array", "items": {"type": "string"}, "nullable": true}}, "additionalProperties": false}, "UserDtoPagedResult": {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/components/schemas/UserDto"}, "nullable": true}, "totalCount": {"type": "integer", "format": "int32"}, "page": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "totalPages": {"type": "integer", "format": "int32"}, "hasPreviousPage": {"type": "boolean"}, "hasNextPage": {"type": "boolean"}, "previousPage": {"type": "integer", "format": "int32", "nullable": true, "readOnly": true}, "nextPage": {"type": "integer", "format": "int32", "nullable": true, "readOnly": true}, "startIndex": {"type": "integer", "format": "int32", "readOnly": true}, "endIndex": {"type": "integer", "format": "int32", "readOnly": true}, "isEmpty": {"type": "boolean", "readOnly": true}, "isFirstPage": {"type": "boolean", "readOnly": true}, "isLastPage": {"type": "boolean", "readOnly": true}}, "additionalProperties": false}, "UserDtoPagedResultApiResponse": {"type": "object", "properties": {"success": {"type": "boolean"}, "data": {"$ref": "#/components/schemas/UserDtoPagedResult"}, "message": {"type": "string", "nullable": true}, "errors": {"type": "array", "items": {"type": "string"}, "nullable": true}}, "additionalProperties": false}, "UserInfoDto": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "firstName": {"type": "string", "nullable": true}, "lastName": {"type": "string", "nullable": true}, "email": {"type": "string", "nullable": true}, "role": {"type": "string", "nullable": true}, "fullName": {"type": "string", "nullable": true}}, "additionalProperties": false}, "UserInfoDtoApiResponse": {"type": "object", "properties": {"success": {"type": "boolean"}, "data": {"$ref": "#/components/schemas/UserInfoDto"}, "message": {"type": "string", "nullable": true}, "errors": {"type": "array", "items": {"type": "string"}, "nullable": true}}, "additionalProperties": false}, "UserRole": {"enum": [1, 2, 3], "type": "integer", "format": "int32"}}, "securitySchemes": {"Bearer": {"type": "<PERSON><PERSON><PERSON><PERSON>", "description": "JWT Authorization header using the <PERSON><PERSON> scheme. Example: \"Authorization: Bearer {token}\"", "name": "Authorization", "in": "header"}}}, "security": [{"Bearer": []}]}