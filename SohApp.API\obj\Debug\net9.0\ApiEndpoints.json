[{"ContainingType": "SohApp.API.Controllers.OrdersController", "Method": "GetOrders", "RelativePath": "api/Orders", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "Page", "Type": "System.Int32", "IsRequired": false}, {"Name": "PageSize", "Type": "System.Int32", "IsRequired": false}, {"Name": "UserId", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=9.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "Status", "Type": "System.String", "IsRequired": false}, {"Name": "StartDate", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=9.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "EndDate", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=9.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "SearchTerm", "Type": "System.String", "IsRequired": false}, {"Name": "SortBy", "Type": "System.String", "IsRequired": false}, {"Name": "SortDescending", "Type": "System.Boolean", "IsRequired": false}], "ReturnTypes": [{"Type": "SohApp.API.Models.ApiResponse`1[[SohApp.Application.Common.PagedResult`1[[SohApp.Application.DTOs.Order.OrderDto, SohApp.Application, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], SohApp.Application, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 200}, {"Type": "SohApp.API.Models.ErrorResponse", "MediaTypes": ["application/json"], "StatusCode": 400}]}, {"ContainingType": "SohApp.API.Controllers.OrdersController", "Method": "CreateOrder", "RelativePath": "api/Orders", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "dto", "Type": "SohApp.Application.DTOs.Order.CreateOrderDto", "IsRequired": true}], "ReturnTypes": [{"Type": "SohApp.API.Models.ApiResponse`1[[System.Int32, System.Private.CoreLib, Version=9.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["application/json"], "StatusCode": 201}, {"Type": "SohApp.API.Models.ErrorResponse", "MediaTypes": ["application/json"], "StatusCode": 400}]}, {"ContainingType": "SohApp.API.Controllers.OrdersController", "Method": "GetOrder", "RelativePath": "api/Orders/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "SohApp.API.Models.ApiResponse`1[[SohApp.Application.Features.Orders.Queries.GetOrder.OrderDetailDto, SohApp.Application, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 200}, {"Type": "SohApp.API.Models.ErrorResponse", "MediaTypes": ["application/json"], "StatusCode": 404}]}, {"ContainingType": "SohApp.API.Controllers.OrdersController", "Method": "GetPendingOrders", "RelativePath": "api/Orders/pending", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "page", "Type": "System.Int32", "IsRequired": false}, {"Name": "pageSize", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "SohApp.API.Models.ApiResponse`1[[SohApp.Application.Common.PagedResult`1[[SohApp.Application.DTOs.Order.OrderDto, SohApp.Application, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], SohApp.Application, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 200}, {"Type": "SohApp.API.Models.ErrorResponse", "MediaTypes": ["application/json"], "StatusCode": 400}]}, {"ContainingType": "SohApp.API.Controllers.OrdersController", "Method": "GetOrdersByUser", "RelativePath": "api/Orders/user/{userId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "userId", "Type": "System.Int32", "IsRequired": true}, {"Name": "page", "Type": "System.Int32", "IsRequired": false}, {"Name": "pageSize", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "SohApp.API.Models.ApiResponse`1[[SohApp.Application.Common.PagedResult`1[[SohApp.Application.DTOs.Order.OrderDto, SohApp.Application, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], SohApp.Application, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 200}, {"Type": "SohApp.API.Models.ErrorResponse", "MediaTypes": ["application/json"], "StatusCode": 400}]}, {"ContainingType": "SohApp.API.Controllers.ProductsController", "Method": "GetProducts", "RelativePath": "api/Products", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "Page", "Type": "System.Int32", "IsRequired": false}, {"Name": "PageSize", "Type": "System.Int32", "IsRequired": false}, {"Name": "CategoryId", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=9.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "SearchTerm", "Type": "System.String", "IsRequired": false}, {"Name": "MinPrice", "Type": "System.Nullable`1[[System.Decimal, System.Private.CoreLib, Version=9.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "MaxPrice", "Type": "System.Nullable`1[[System.Decimal, System.Private.CoreLib, Version=9.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "InStock", "Type": "System.Nullable`1[[<PERSON><PERSON>Boolean, System.Private.CoreLib, Version=9.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "SortBy", "Type": "System.String", "IsRequired": false}, {"Name": "SortDescending", "Type": "System.Boolean", "IsRequired": false}], "ReturnTypes": [{"Type": "SohApp.API.Models.ApiResponse`1[[SohApp.Application.Common.PagedResult`1[[SohApp.Application.DTOs.Product.ProductDto, SohApp.Application, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], SohApp.Application, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 200}, {"Type": "SohApp.API.Models.ErrorResponse", "MediaTypes": ["application/json"], "StatusCode": 400}]}, {"ContainingType": "SohApp.API.Controllers.ProductsController", "Method": "CreateProduct", "RelativePath": "api/Products", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "dto", "Type": "SohApp.Application.DTOs.Product.CreateProductDto", "IsRequired": true}], "ReturnTypes": [{"Type": "SohApp.API.Models.ApiResponse`1[[System.Int32, System.Private.CoreLib, Version=9.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["application/json"], "StatusCode": 201}, {"Type": "SohApp.API.Models.ErrorResponse", "MediaTypes": ["application/json"], "StatusCode": 400}]}, {"ContainingType": "SohApp.API.Controllers.ProductsController", "Method": "GetProduct", "RelativePath": "api/Products/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "SohApp.API.Models.ApiResponse`1[[SohApp.Application.DTOs.Product.ProductDto, SohApp.Application, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 200}, {"Type": "SohApp.API.Models.ErrorResponse", "MediaTypes": ["application/json"], "StatusCode": 404}]}, {"ContainingType": "SohApp.API.Controllers.UsersController", "Method": "GetUsers", "RelativePath": "api/Users", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "Page", "Type": "System.Int32", "IsRequired": false}, {"Name": "PageSize", "Type": "System.Int32", "IsRequired": false}, {"Name": "Role", "Type": "System.String", "IsRequired": false}, {"Name": "IsActive", "Type": "System.Nullable`1[[<PERSON><PERSON>Boolean, System.Private.CoreLib, Version=9.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "SearchTerm", "Type": "System.String", "IsRequired": false}, {"Name": "SortBy", "Type": "System.String", "IsRequired": false}, {"Name": "SortDescending", "Type": "System.Boolean", "IsRequired": false}], "ReturnTypes": [{"Type": "SohApp.API.Models.ApiResponse`1[[SohApp.Application.Common.PagedResult`1[[SohApp.Application.DTOs.User.UserDto, SohApp.Application, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], SohApp.Application, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 200}, {"Type": "SohApp.API.Models.ErrorResponse", "MediaTypes": ["application/json"], "StatusCode": 400}, {"Type": "SohApp.API.Models.ErrorResponse", "MediaTypes": ["application/json"], "StatusCode": 401}]}, {"ContainingType": "SohApp.API.Controllers.UsersController", "Method": "CreateUser", "RelativePath": "api/Users", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "dto", "Type": "SohApp.Application.DTOs.User.CreateUserDto", "IsRequired": true}], "ReturnTypes": [{"Type": "SohApp.API.Models.ApiResponse`1[[System.Int32, System.Private.CoreLib, Version=9.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["application/json"], "StatusCode": 201}, {"Type": "SohApp.API.Models.ErrorResponse", "MediaTypes": ["application/json"], "StatusCode": 400}, {"Type": "SohApp.API.Models.ErrorResponse", "MediaTypes": ["application/json"], "StatusCode": 401}, {"Type": "SohApp.API.Models.ErrorResponse", "MediaTypes": ["application/json"], "StatusCode": 409}, {"Type": "SohApp.API.Models.ErrorResponse", "MediaTypes": ["application/json"], "StatusCode": 422}]}, {"ContainingType": "SohApp.API.Controllers.UsersController", "Method": "GetUser", "RelativePath": "api/Users/<USER>", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "SohApp.API.Models.ApiResponse`1[[SohApp.Application.DTOs.User.UserDto, SohApp.Application, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 200}, {"Type": "SohApp.API.Models.ErrorResponse", "MediaTypes": ["application/json"], "StatusCode": 400}, {"Type": "SohApp.API.Models.ErrorResponse", "MediaTypes": ["application/json"], "StatusCode": 401}, {"Type": "SohApp.API.Models.ErrorResponse", "MediaTypes": ["application/json"], "StatusCode": 404}]}, {"ContainingType": "SohApp.API.Controllers.UsersController", "Method": "UpdateUser", "RelativePath": "api/Users/<USER>", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "dto", "Type": "SohApp.Application.DTOs.User.UpdateUserDto", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 204}, {"Type": "SohApp.API.Models.ErrorResponse", "MediaTypes": ["application/json"], "StatusCode": 400}, {"Type": "SohApp.API.Models.ErrorResponse", "MediaTypes": ["application/json"], "StatusCode": 401}, {"Type": "SohApp.API.Models.ErrorResponse", "MediaTypes": ["application/json"], "StatusCode": 404}, {"Type": "SohApp.API.Models.ErrorResponse", "MediaTypes": ["application/json"], "StatusCode": 422}]}, {"ContainingType": "SohApp.API.Controllers.UsersController", "Method": "DeleteUser", "RelativePath": "api/Users/<USER>", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 204}, {"Type": "SohApp.API.Models.ErrorResponse", "MediaTypes": ["application/json"], "StatusCode": 400}, {"Type": "SohApp.API.Models.ErrorResponse", "MediaTypes": ["application/json"], "StatusCode": 401}, {"Type": "SohApp.API.Models.ErrorResponse", "MediaTypes": ["application/json"], "StatusCode": 403}, {"Type": "SohApp.API.Models.ErrorResponse", "MediaTypes": ["application/json"], "StatusCode": 404}]}]