using MediatR;
using SohApp.Application.Common;
using SohApp.Application.DTOs.Order;
using SohApp.Core.Enums;

namespace SohApp.Application.Features.Orders.Queries.GetOrders;

public class GetOrdersQuery : IRequest<Result<PagedResult<OrderDto>>>
{
    public int Page { get; set; } = 1;
    public int PageSize { get; set; } = 10;
    public int? UserId { get; set; }
    public OrderStatus? Status { get; set; }
    public DateTime? StartDate { get; set; }
    public DateTime? EndDate { get; set; }
    public string? SearchTerm { get; set; }
    public string? SortBy { get; set; }
    public bool SortDescending { get; set; } = true; // Default to newest first
}
