using Microsoft.EntityFrameworkCore;
using SohApp.Core.Entities;
using SohApp.Core.Interfaces.Repositories;
using SohApp.Infrastructure.Data;

namespace SohApp.Infrastructure.Repositories;

public class ProductRepository : GenericRepository<Product>, IProductRepository
{
    public ProductRepository(ApplicationDbContext context) : base(context)
    {
    }

    public async Task<IReadOnlyList<Product>> GetByCategoryAsync(int categoryId, CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .Include(p => p.Category)
            .Where(p => p.CategoryId == categoryId && p.IsActive)
            .OrderBy(p => p.Name)
            .AsNoTracking()
            .ToListAsync(cancellationToken);
    }

    public async Task<IReadOnlyList<Product>> GetInStockAsync(CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .Where(p => p.StockQuantity > 0 && p.IsActive)
            .OrderByDescending(p => p.StockQuantity)
            .AsNoTracking()
            .ToListAsync(cancellationToken);
    }

    public async Task<IReadOnlyList<Product>> GetByPriceRangeAsync(decimal minPrice, decimal maxPrice, CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .Where(p => p.Price.Amount >= minPrice && p.Price.Amount <= maxPrice && p.IsActive)
            .OrderBy(p => p.Price.Amount)
            .AsNoTracking()
            .ToListAsync(cancellationToken);
    }

    public async Task<Product?> GetBySkuAsync(string sku, CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .Include(p => p.Category)
            .FirstOrDefaultAsync(p => p.Sku == sku, cancellationToken);
    }
}
