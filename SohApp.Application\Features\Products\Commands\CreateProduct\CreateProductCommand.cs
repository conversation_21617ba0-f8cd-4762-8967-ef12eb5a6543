using MediatR;
using SohApp.Application.Common;

namespace SohApp.Application.Features.Products.Commands.CreateProduct;

public class CreateProductCommand : IRequest<Result<int>>
{
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string Sku { get; set; } = string.Empty;
    public decimal Price { get; set; }
    public string Currency { get; set; } = "USD";
    public int StockQuantity { get; set; }
    public int CategoryId { get; set; }
}
