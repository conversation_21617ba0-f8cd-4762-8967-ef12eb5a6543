using SohApp.Core.Constants;
using SohApp.Core.Entities.Base;
using SohApp.Core.Events.ProductEvents;
using SohApp.Core.Exceptions;
using SohApp.Core.ValueObjects;

namespace SohApp.Core.Entities;

public class Product : BaseEntity
{
    private string _name = string.Empty;
    private string _description = string.Empty;
    private string _sku = string.Empty;
    private int _stockQuantity;

    public string Name
    {
        get => _name;
        set => _name = ValidateName(value);
    }

    public string Description
    {
        get => _description;
        set => _description = ValidateDescription(value);
    }

    public string Sku
    {
        get => _sku;
        set => _sku = ValidateSku(value);
    }

    public Money Price { get; set; } = null!;

    public int StockQuantity
    {
        get => _stockQuantity;
        private set => _stockQuantity = value; // Private setter to enforce business rules
    }

    public int CategoryId { get; set; }
    public bool IsActive { get; set; } = true;

    // Navigation properties - removed virtual to prevent lazy loading issues
    public Category Category { get; set; } = null!;
    public ICollection<OrderItem> OrderItems { get; set; } = new List<OrderItem>();

    // Computed properties
    public bool IsInStock => StockQuantity > 0;
    public bool IsLowStock => StockQuantity > 0 && StockQuantity <= DomainConstants.Product.LowStockThreshold;

    /// <summary>
    /// Updates the stock quantity by the specified amount
    /// </summary>
    /// <param name="quantity">The quantity to add (positive) or remove (negative)</param>
    /// <exception cref="BusinessRuleException">Thrown when the operation would result in negative stock</exception>
    public void UpdateStock(int quantity)
    {
        if (quantity == 0) return; // No change needed

        var newStock = StockQuantity + quantity;
        if (newStock < 0)
            throw new BusinessRuleException($"Insufficient stock. Available: {StockQuantity}, Requested: {Math.Abs(quantity)}");

        var oldStock = StockQuantity;
        StockQuantity = newStock;

        AddDomainEvent(new ProductStockUpdatedEvent(Id, oldStock, StockQuantity));

        // Additional events for stock level changes
        if (oldStock > 0 && StockQuantity == 0)
            AddDomainEvent(new ProductOutOfStockEvent(Id, Name));
        else if (oldStock == 0 && StockQuantity > 0)
            AddDomainEvent(new ProductBackInStockEvent(Id, Name, StockQuantity));
        else if (!IsLowStock && oldStock > DomainConstants.Product.LowStockThreshold && IsLowStock)
            AddDomainEvent(new ProductLowStockEvent(Id, Name, StockQuantity));
    }

    /// <summary>
    /// Sets the stock quantity to a specific value
    /// </summary>
    /// <param name="newQuantity">The new stock quantity</param>
    /// <exception cref="ArgumentException">Thrown when quantity is negative</exception>
    public void SetStock(int newQuantity)
    {
        if (newQuantity < 0)
            throw new ArgumentException("Stock quantity cannot be negative", nameof(newQuantity));

        var difference = newQuantity - StockQuantity;
        if (difference != 0)
        {
            UpdateStock(difference);
        }
    }

    /// <summary>
    /// Updates the product price
    /// </summary>
    /// <param name="newPrice">The new price</param>
    /// <exception cref="ArgumentNullException">Thrown when newPrice is null</exception>
    /// <exception cref="BusinessRuleException">Thrown when price change is not allowed</exception>
    public void UpdatePrice(Money newPrice)
    {
        ArgumentNullException.ThrowIfNull(newPrice);

        if (Price.Equals(newPrice)) return; // No change needed

        // Business rule: Cannot set price to zero or negative
        if (newPrice.Amount <= 0)
            throw new BusinessRuleException("Product price must be greater than zero");

        var oldPrice = Price;
        Price = newPrice;
        AddDomainEvent(new ProductPriceChangedEvent(Id, oldPrice, newPrice));
    }

    /// <summary>
    /// Activates the product
    /// </summary>
    /// <exception cref="BusinessRuleException">Thrown when product is already active</exception>
    public void Activate()
    {
        if (IsActive)
            throw new BusinessRuleException("Product is already active");

        IsActive = true;
        AddDomainEvent(new ProductActivatedEvent(Id, Name));
    }

    /// <summary>
    /// Deactivates the product
    /// </summary>
    /// <exception cref="BusinessRuleException">Thrown when product is already inactive</exception>
    public void Deactivate()
    {
        if (!IsActive)
            throw new BusinessRuleException("Product is already inactive");

        IsActive = false;
        AddDomainEvent(new ProductDeactivatedEvent(Id, Name));
    }

    /// <summary>
    /// Reserves stock for an order
    /// </summary>
    /// <param name="quantity">The quantity to reserve</param>
    /// <exception cref="BusinessRuleException">Thrown when insufficient stock is available</exception>
    public void ReserveStock(int quantity)
    {
        if (quantity <= 0)
            throw new ArgumentException("Quantity must be positive", nameof(quantity));

        if (!IsActive)
            throw new BusinessRuleException("Cannot reserve stock for inactive product");

        UpdateStock(-quantity); // This will validate stock availability
    }

    /// <summary>
    /// Validates the product name
    /// </summary>
    private static string ValidateName(string name)
    {
        if (string.IsNullOrWhiteSpace(name))
            throw new ArgumentException("Product name cannot be empty");

        if (name.Length > DomainConstants.Product.NameMaxLength)
            throw new ArgumentException($"Product name cannot exceed {DomainConstants.Product.NameMaxLength} characters");

        return name.Trim();
    }

    /// <summary>
    /// Validates the product description
    /// </summary>
    private static string ValidateDescription(string description)
    {
        if (string.IsNullOrWhiteSpace(description))
            return string.Empty;

        if (description.Length > DomainConstants.Product.DescriptionMaxLength)
            throw new ArgumentException($"Product description cannot exceed {DomainConstants.Product.DescriptionMaxLength} characters");

        return description.Trim();
    }

    /// <summary>
    /// Validates the product SKU
    /// </summary>
    private static string ValidateSku(string sku)
    {
        if (string.IsNullOrWhiteSpace(sku))
            throw new ArgumentException("Product SKU cannot be empty");

        if (sku.Length > DomainConstants.Product.SkuMaxLength)
            throw new ArgumentException($"Product SKU cannot exceed {DomainConstants.Product.SkuMaxLength} characters");

        // SKU should be uppercase and contain only alphanumeric characters and hyphens
        var cleanSku = sku.Trim().ToUpperInvariant();
        if (!System.Text.RegularExpressions.Regex.IsMatch(cleanSku, @"^[A-Z0-9\-]+$"))
            throw new ArgumentException("Product SKU can only contain letters, numbers, and hyphens");

        return cleanSku;
    }
}
