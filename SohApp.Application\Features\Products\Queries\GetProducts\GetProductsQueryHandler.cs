using AutoMapper;
using MediatR;
using Microsoft.Extensions.Logging;
using SohApp.Application.Common;
using SohApp.Application.DTOs.Product;
using SohApp.Application.Features.Products.Queries.GetProducts.Specifications;
using SohApp.Core.Interfaces;

namespace SohApp.Application.Features.Products.Queries.GetProducts;

public class GetProductsQueryHandler : IRequestHandler<GetProductsQuery, Result<PagedResult<ProductDto>>>
{
    private readonly IUnitOfWork _unitOfWork;
    private readonly IMapper _mapper;
    private readonly ILogger<GetProductsQueryHandler> _logger;

    public GetProductsQueryHandler(
        IUnitOfWork unitOfWork,
        IMapper mapper,
        ILogger<GetProductsQueryHandler> logger)
    {
        _unitOfWork = unitOfWork;
        _mapper = mapper;
        _logger = logger;
    }

    public async Task<Result<PagedResult<ProductDto>>> Handle(GetProductsQuery request, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogDebug("Retrieving products with filters: CategoryId={CategoryId}, SearchTerm={SearchTerm}, MinPrice={MinPrice}, MaxPrice={MaxPrice}, InStock={InStock}, Page={Page}, PageSize={PageSize}",
                request.CategoryId, request.SearchTerm, request.MinPrice, request.MaxPrice, request.InStock, request.Page, request.PageSize);

            // Validate pagination parameters
            if (request.Page < 1)
                return Result<PagedResult<ProductDto>>.Failure("Page number must be greater than 0");

            if (request.PageSize < 1 || request.PageSize > 100)
                return Result<PagedResult<ProductDto>>.Failure("Page size must be between 1 and 100");

            // Validate price range
            if (request.MinPrice.HasValue && request.MaxPrice.HasValue && request.MinPrice > request.MaxPrice)
                return Result<PagedResult<ProductDto>>.Failure("Minimum price cannot be greater than maximum price");

            var spec = new GetProductsSpecification(
                request.CategoryId,
                request.SearchTerm,
                request.MinPrice,
                request.MaxPrice,
                request.InStock,
                request.SortBy,
                request.SortDescending);

            // Get total count first (without pagination)
            var totalCount = await _unitOfWork.Products.CountAsync(spec, cancellationToken);

            // Apply pagination to specification
            spec.ApplyPaging((request.Page - 1) * request.PageSize, request.PageSize);

            // Get paginated data
            var products = await _unitOfWork.Products.GetAsync(spec, cancellationToken);
            var productDtos = _mapper.Map<IReadOnlyList<ProductDto>>(products);

            var result = new PagedResult<ProductDto>
            {
                Data = productDtos,
                TotalCount = totalCount,
                Page = request.Page,
                PageSize = request.PageSize,
                TotalPages = (int)Math.Ceiling(totalCount / (double)request.PageSize),
                HasNextPage = request.Page < (int)Math.Ceiling(totalCount / (double)request.PageSize),
                HasPreviousPage = request.Page > 1
            };

            _logger.LogDebug("Successfully retrieved {Count} products out of {TotalCount} total",
                productDtos.Count, totalCount);

            return Result<PagedResult<ProductDto>>.Success(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Unexpected error retrieving products with filters: CategoryId={CategoryId}, SearchTerm={SearchTerm}",
                request.CategoryId, request.SearchTerm);
            return Result<PagedResult<ProductDto>>.Failure("An unexpected error occurred while retrieving products. Please try again.");
        }
    }
}
