namespace SohApp.Core.ValueObjects;

public class Percentage : IEquatable<Percentage>, IComparable<Percentage>
{
    public decimal Value { get; }
    public decimal AsDecimal => Value / 100m;
    public double AsDouble => (double)(Value / 100m);

    public Percentage(decimal value)
    {
        if (value < 0)
            throw new ArgumentException("Percentage cannot be negative", nameof(value));
        
        if (value > 100)
            throw new ArgumentException("Percentage cannot exceed 100%", nameof(value));

        Value = Math.Round(value, 2);
    }

    public static Percentage FromDecimal(decimal decimalValue)
    {
        if (decimalValue < 0 || decimalValue > 1)
            throw new ArgumentException("Decimal value must be between 0 and 1", nameof(decimalValue));

        return new Percentage(decimalValue * 100);
    }

    public static Percentage Zero => new(0);
    public static Percentage OneHundred => new(100);

    public Percentage Add(Percentage other)
    {
        var result = Value + other.Value;
        if (result > 100)
            throw new InvalidOperationException("Result would exceed 100%");
        
        return new Percentage(result);
    }

    public Percentage Subtract(Percentage other)
    {
        var result = Value - other.Value;
        if (result < 0)
            throw new InvalidOperationException("Result would be negative");
        
        return new Percentage(result);
    }

    public Money CalculateOf(Money amount)
    {
        var calculatedAmount = amount.Amount * AsDecimal;
        return new Money(calculatedAmount, amount.Currency);
    }

    public decimal CalculateOf(decimal amount)
    {
        return amount * AsDecimal;
    }

    public Percentage Multiply(decimal factor)
    {
        if (factor < 0)
            throw new ArgumentException("Factor cannot be negative", nameof(factor));

        var result = Value * factor;
        if (result > 100)
            throw new InvalidOperationException("Result would exceed 100%");

        return new Percentage(result);
    }

    public bool IsZero => Value == 0;
    public bool IsOneHundred => Value == 100;

    public bool Equals(Percentage? other) => 
        other is not null && Value == other.Value;

    public override bool Equals(object? obj) => Equals(obj as Percentage);

    public override int GetHashCode() => Value.GetHashCode();

    public int CompareTo(Percentage? other)
    {
        if (other is null) return 1;
        return Value.CompareTo(other.Value);
    }

    public override string ToString() => $"{Value:F2}%";

    public string ToString(string format) => Value.ToString(format) + "%";

    // Operators
    public static Percentage operator +(Percentage left, Percentage right) => left.Add(right);
    public static Percentage operator -(Percentage left, Percentage right) => left.Subtract(right);
    public static Percentage operator *(Percentage left, decimal right) => left.Multiply(right);
    
    public static bool operator ==(Percentage left, Percentage right) => 
        left?.Equals(right) ?? right is null;
    public static bool operator !=(Percentage left, Percentage right) => !(left == right);
    
    public static bool operator <(Percentage left, Percentage right) => 
        left?.CompareTo(right) < 0;
    public static bool operator >(Percentage left, Percentage right) => 
        left?.CompareTo(right) > 0;
    public static bool operator <=(Percentage left, Percentage right) => 
        left?.CompareTo(right) <= 0;
    public static bool operator >=(Percentage left, Percentage right) => 
        left?.CompareTo(right) >= 0;

    // Implicit conversions
    public static implicit operator decimal(Percentage percentage) => percentage.Value;
    public static explicit operator Percentage(decimal value) => new(value);
}
