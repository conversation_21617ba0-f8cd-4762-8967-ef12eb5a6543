using MediatR;
using SohApp.Application.Common;
using SohApp.Application.DTOs.Order;

namespace SohApp.Application.Features.Orders.Queries.GetOrder;

public class GetOrderQuery : IRequest<Result<OrderDetailDto>>
{
    public int Id { get; set; }
}

// Detailed order DTO with items
public class OrderDetailDto
{
    public int Id { get; set; }
    public string OrderNumber { get; set; } = string.Empty;
    public string Status { get; set; } = string.Empty;
    public decimal TotalAmount { get; set; }
    public string Currency { get; set; } = string.Empty;
    public int UserId { get; set; }
    public string UserName { get; set; } = string.Empty;
    public string UserEmail { get; set; } = string.Empty;
    public DateTime CreatedAt { get; set; }
    public DateTime? ShippedAt { get; set; }
    public DateTime? DeliveredAt { get; set; }
    public List<OrderItemDto> Items { get; set; } = new();
}
