using SohApp.Core.Events.Base;

namespace SohApp.Core.Events.OrderEvents;

public class OrderItemQuantityUpdatedEvent : DomainEvent
{
    public int OrderId { get; }
    public int ProductId { get; }
    public int NewQuantity { get; }

    public OrderItemQuantityUpdatedEvent(int orderId, int productId, int newQuantity)
    {
        OrderId = orderId;
        ProductId = productId;
        NewQuantity = newQuantity;
    }
}
