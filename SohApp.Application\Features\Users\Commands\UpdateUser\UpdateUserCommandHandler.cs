using MediatR;
using Microsoft.Extensions.Logging;
using SohApp.Application.Common;
using SohApp.Core.Events.UserEvents;
using SohApp.Core.Interfaces;

namespace SohApp.Application.Features.Users.Commands.UpdateUser;

public class UpdateUserCommandHandler : IRequestHandler<UpdateUserCommand, Result>
{
    private readonly IUnitOfWork _unitOfWork;
    private readonly ILogger<UpdateUserCommandHandler> _logger;

    public UpdateUserCommandHandler(
        IUnitOfWork unitOfWork,
        ILogger<UpdateUserCommandHandler> logger)
    {
        _unitOfWork = unitOfWork;
        _logger = logger;
    }

    public async Task<Result> Handle(UpdateUserCommand request, CancellationToken cancellationToken)
    {
        try
        {
            var user = await _unitOfWork.Users.GetByIdAsync(request.Id, cancellationToken);
            
            if (user == null)
            {
                return Result.Failure("User not found");
            }

            // Update user properties
            user.UpdateProfile(request.FirstName, request.LastName);
            user.Role = request.Role;
            
            if (request.IsActive != user.IsActive)
            {
                if (request.IsActive)
                    user.Activate();
                else
                    user.Deactivate();
            }

            _unitOfWork.Users.Update(user);
            await _unitOfWork.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("User updated successfully with ID: {UserId}", user.Id);

            return Result.Success();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating user with ID: {UserId}", request.Id);
            return Result.Failure("An error occurred while updating the user");
        }
    }
}
