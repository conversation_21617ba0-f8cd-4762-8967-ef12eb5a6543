{"ConnectionStrings": {"DefaultConnection": "Server=(localdb)\\mssqllocaldb;Database=SohAppDb;Trusted_Connection=true;MultipleActiveResultSets=true;TrustServerCertificate=true;Encrypt=false", "AlternativeLocalDb": "Server=(localdb)\\mssqllocaldb;Database=SohAppDb;Integrated Security=true;TrustServerCertificate=true;Encrypt=false", "AlternativeSqlServer": "Server=localhost;Database=SohAppDb;Trusted_Connection=true;TrustServerCertificate=true;Encrypt=false"}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning", "Microsoft.EntityFrameworkCore": "Warning"}}, "AllowedHosts": "*"}