using SohApp.Core.Entities.Base;

namespace SohApp.Core.Entities;

public class RefreshToken : BaseEntity
{
    public string Token { get; set; } = string.Empty;
    public DateTime ExpiresAt { get; set; }
    public bool IsRevoked { get; set; }
    public DateTime? RevokedAt { get; set; }
    public string? RevokedByIp { get; set; }
    public string? ReplacedByToken { get; set; }
    public string? ReasonRevoked { get; set; }
    public int UserId { get; set; }
    
    // Navigation properties
    public User User { get; set; } = null!;
    
    public bool IsExpired => DateTime.UtcNow >= ExpiresAt;
    public bool IsActive => !IsRevoked && !IsExpired;
    
    /// <summary>
    /// Revokes the refresh token
    /// </summary>
    /// <param name="ipAddress">IP address that revoked the token</param>
    /// <param name="reason">Reason for revocation</param>
    /// <param name="replacedByToken">Token that replaced this one (if any)</param>
    public void Revoke(string? ipAddress = null, string? reason = null, string? replacedByToken = null)
    {
        IsRevoked = true;
        RevokedAt = DateTime.UtcNow;
        RevokedByIp = ipAddress;
        ReasonRevoked = reason;
        ReplacedByToken = replacedByToken;
    }
}
