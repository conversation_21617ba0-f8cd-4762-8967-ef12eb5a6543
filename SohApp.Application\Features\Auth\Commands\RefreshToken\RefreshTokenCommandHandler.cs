using MediatR;
using Microsoft.Extensions.Logging;
using SohApp.Application.Common;
using SohApp.Application.DTOs.Auth;
using SohApp.Application.Interfaces;

namespace SohApp.Application.Features.Auth.Commands.RefreshToken;

public class RefreshTokenCommandHandler : IRequestHandler<RefreshTokenCommand, Result<RefreshTokenResponseDto>>
{
    private readonly IAuthenticationService _authenticationService;
    private readonly ILogger<RefreshTokenCommandHandler> _logger;

    public RefreshTokenCommandHandler(
        IAuthenticationService authenticationService,
        ILogger<RefreshTokenCommandHandler> logger)
    {
        _authenticationService = authenticationService;
        _logger = logger;
    }

    public async Task<Result<RefreshTokenResponseDto>> Handle(RefreshTokenCommand request, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Processing refresh token request");

        var refreshTokenDto = new RefreshTokenDto
        {
            RefreshToken = request.RefreshToken
        };

        var result = await _authenticationService.RefreshTokenAsync(refreshTokenDto, request.IpAddress, cancellationToken);

        if (result.IsSuccess)
        {
            _logger.LogInformation("Token refresh successful");
        }
        else
        {
            _logger.LogWarning("Token refresh failed. Errors: {Errors}", string.Join(", ", result.Errors));
        }

        return result;
    }
}
