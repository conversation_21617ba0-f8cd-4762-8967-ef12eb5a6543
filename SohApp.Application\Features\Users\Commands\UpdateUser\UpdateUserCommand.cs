using MediatR;
using SohApp.Application.Common;
using SohApp.Core.Enums;

namespace SohApp.Application.Features.Users.Commands.UpdateUser;

public class UpdateUserCommand : IRequest<Result>
{
    public int Id { get; set; }
    public string FirstName { get; set; } = string.Empty;
    public string LastName { get; set; } = string.Empty;
    public UserRole Role { get; set; }
    public bool IsActive { get; set; }
}
