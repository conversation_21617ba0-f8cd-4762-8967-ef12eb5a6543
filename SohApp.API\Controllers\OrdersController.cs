using Microsoft.AspNetCore.Mvc;
using SohApp.API.Controllers.Base;
using SohApp.API.Models;
using SohApp.Application.Common;
using SohApp.Application.DTOs.Order;
using SohApp.Application.Features.Orders.Commands.CreateOrder;
using SohApp.Application.Features.Orders.Queries.GetOrder;
using SohApp.Application.Features.Orders.Queries.GetOrders;

namespace SohApp.API.Controllers;

[Route("api/[controller]")]
public class OrdersController : BaseController
{
    /// <summary>
    /// Get all orders with optional filtering and pagination
    /// </summary>
    [HttpGet]
    [ProducesResponseType(typeof(ApiResponse<PagedResult<OrderDto>>), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ErrorResponse), StatusCodes.Status400BadRequest)]
    public async Task<ActionResult<ApiResponse<PagedResult<OrderDto>>>> GetOrders(
        [FromQuery] GetOrdersQuery query)
    {
        var result = await Mediator.Send(query);
        return HandleApiResponse(result);
    }

    /// <summary>
    /// Get order by ID with full details
    /// </summary>
    [HttpGet("{id:int}")]
    [ProducesResponseType(typeof(ApiResponse<OrderDetailDto>), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ErrorResponse), StatusCodes.Status404NotFound)]
    public async Task<ActionResult<ApiResponse<OrderDetailDto>>> GetOrder(int id)
    {
        var query = new GetOrderQuery { Id = id };
        var result = await Mediator.Send(query);
        return HandleApiResponse(result);
    }

    /// <summary>
    /// Get orders for a specific user
    /// </summary>
    [HttpGet("user/{userId:int}")]
    [ProducesResponseType(typeof(ApiResponse<PagedResult<OrderDto>>), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ErrorResponse), StatusCodes.Status400BadRequest)]
    public async Task<ActionResult<ApiResponse<PagedResult<OrderDto>>>> GetOrdersByUser(
        int userId,
        [FromQuery] int page = 1,
        [FromQuery] int pageSize = 10)
    {
        var query = new GetOrdersQuery 
        { 
            UserId = userId,
            Page = page,
            PageSize = pageSize
        };
        var result = await Mediator.Send(query);
        return HandleApiResponse(result);
    }

    /// <summary>
    /// Create a new order
    /// </summary>
    [HttpPost]
    [ProducesResponseType(typeof(ApiResponse<int>), StatusCodes.Status201Created)]
    [ProducesResponseType(typeof(ErrorResponse), StatusCodes.Status400BadRequest)]
    public async Task<ActionResult<ApiResponse<int>>> CreateOrder([FromBody] CreateOrderDto dto)
    {
        var command = new CreateOrderCommand
        {
            UserId = dto.UserId,
            Items = dto.Items.Select(i => new CreateOrderItemCommand
            {
                ProductId = i.ProductId,
                Quantity = i.Quantity
            }).ToList()
        };

        var result = await Mediator.Send(command);
        
        if (result.IsSuccess)
        {
            return CreatedAtActionWithApiResponse(nameof(GetOrder), new { id = result.Value }, result);
        }

        return HandleApiResponse(result);
    }

    /// <summary>
    /// Get pending orders (for admin/management)
    /// </summary>
    [HttpGet("pending")]
    [ProducesResponseType(typeof(ApiResponse<PagedResult<OrderDto>>), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ErrorResponse), StatusCodes.Status400BadRequest)]
    public async Task<ActionResult<ApiResponse<PagedResult<OrderDto>>>> GetPendingOrders(
        [FromQuery] int page = 1,
        [FromQuery] int pageSize = 10)
    {
        var query = new GetOrdersQuery 
        { 
            Status = Core.Enums.OrderStatus.Pending,
            Page = page,
            PageSize = pageSize
        };
        var result = await Mediator.Send(query);
        return HandleApiResponse(result);
    }
}
