using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using SohApp.Core.Events.Base;
using SohApp.Core.Interfaces;

namespace SohApp.Infrastructure.Services;

public class DomainEventDispatcher : IDomainEventDispatcher
{
    private readonly IServiceProvider _serviceProvider;
    private readonly ILogger<DomainEventDispatcher> _logger;

    public DomainEventDispatcher(IServiceProvider serviceProvider, ILogger<DomainEventDispatcher> logger)
    {
        _serviceProvider = serviceProvider;
        _logger = logger;
    }

    public async Task DispatchAsync(IDomainEvent domainEvent, CancellationToken cancellationToken = default)
    {
        var eventType = domainEvent.GetType();
        var handlerType = typeof(IDomainEventHandler<>).MakeGenericType(eventType);
        
        _logger.LogInformation("Dispatching domain event: {EventType} with ID: {EventId}", 
            eventType.Name, domainEvent.EventId);

        using var scope = _serviceProvider.CreateScope();
        var handlers = scope.ServiceProvider.GetServices(handlerType);

        foreach (var handler in handlers)
        {
            try
            {
                var handleMethod = handlerType.GetMethod("Handle");
                if (handleMethod != null)
                {
                    var task = (Task)handleMethod.Invoke(handler, new object[] { domainEvent, cancellationToken })!;
                    await task;
                    
                    _logger.LogInformation("Successfully handled domain event: {EventType} with handler: {HandlerType}", 
                        eventType.Name, handler.GetType().Name);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error handling domain event: {EventType} with handler: {HandlerType}", 
                    eventType.Name, handler.GetType().Name);
                
                // Continue processing other handlers even if one fails
            }
        }
    }
}
