using SohApp.Core.Entities.Base;
using SohApp.Core.ValueObjects;

namespace SohApp.Core.Entities;

public class OrderItem : BaseEntity
{
    public int OrderId { get; set; }
    public int ProductId { get; set; }
    public int Quantity { get; set; }
    public Money UnitPrice { get; set; } = null!;
    public Money TotalPrice { get; set; } = null!;
    
    // Navigation properties
    public virtual Order Order { get; set; } = null!;
    public virtual Product Product { get; set; } = null!;
    
    public void UpdateQuantity(int newQuantity)
    {
        if (newQuantity <= 0)
            throw new ArgumentException("Quantity must be greater than zero");
        
        Quantity = newQuantity;
        TotalPrice = new Money(UnitPrice.Amount * newQuantity, UnitPrice.Currency);
    }
}
